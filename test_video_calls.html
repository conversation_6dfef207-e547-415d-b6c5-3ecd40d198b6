<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Call Test - Flux Notebook</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        .test-btn.primary {
            background-color: #007bff;
            color: white;
        }
        .test-btn.primary:hover {
            background-color: #0056b3;
        }
        .test-btn.success {
            background-color: #28a745;
            color: white;
        }
        .test-btn.success:hover {
            background-color: #1e7e34;
        }
        .test-btn.warning {
            background-color: #ffc107;
            color: black;
        }
        .test-btn.warning:hover {
            background-color: #e0a800;
        }
        .test-btn.danger {
            background-color: #dc3545;
            color: white;
        }
        .test-btn.danger:hover {
            background-color: #c82333;
        }
        .test-results {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
            display: inline-block;
        }
        .status.pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .iframe-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .iframe-wrapper {
            flex: 1;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .iframe-wrapper h3 {
            margin: 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .iframe-wrapper iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎥 Video Call Testing Suite - Flux Notebook</h1>
        
        <div class="test-section">
            <h2>📋 Test Instructions</h2>
            <p>This page will help you test the video calling functionality comprehensively:</p>
            <ol>
                <li><strong>AI Chat Reopen Test:</strong> Close the AI chat sidebar and verify the reopen button appears</li>
                <li><strong>Video Call Initiation:</strong> Start a video call from one user and verify others can see it</li>
                <li><strong>Camera Auto-Start Prevention:</strong> Ensure other users' cameras don't turn on automatically</li>
                <li><strong>Multi-User Video:</strong> Test video/audio with multiple participants</li>
                <li><strong>Edge Cases:</strong> Test joining/leaving during calls, network issues, etc.</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🔧 Quick Test Controls</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="openMultipleInstances()">Open 2 Test Instances</button>
                <button class="test-btn success" onclick="runAIChatTest()">Test AI Chat Reopen</button>
                <button class="test-btn warning" onclick="runVideoCallTest()">Test Video Calls</button>
                <button class="test-btn danger" onclick="clearResults()">Clear Results</button>
            </div>
            <div class="test-results" id="testResults">
Ready to run tests...
Click "Open 2 Test Instances" to start testing with multiple users.
            </div>
        </div>

        <div class="test-section">
            <h2>👥 Multi-User Test Environment</h2>
            <div class="iframe-container" id="iframeContainer">
                <p>Click "Open 2 Test Instances" to load test environments here.</p>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ Test Checklist</h2>
            <div id="testChecklist">
                <div class="status pending">🔄 AI Chat Reopen Button Visibility</div>
                <div class="status pending">🔄 Video Call Start Notification</div>
                <div class="status pending">🔄 Camera Auto-Start Prevention</div>
                <div class="status pending">🔄 Multi-User Video/Audio</div>
                <div class="status pending">🔄 WebRTC Connection Establishment</div>
                <div class="status pending">🔄 User Join/Leave During Call</div>
                <div class="status pending">🔄 Media Controls (Mute/Unmute)</div>
                <div class="status pending">🔄 Call End Cleanup</div>
            </div>
        </div>
    </div>

    <script>
        let testResults = document.getElementById('testResults');
        let testCount = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            testResults.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            testResults.scrollTop = testResults.scrollHeight;
        }

        function openMultipleInstances() {
            const container = document.getElementById('iframeContainer');
            container.innerHTML = `
                <div class="iframe-wrapper">
                    <h3>👤 User 1 (Alice)</h3>
                    <iframe src="frontend.html" id="user1"></iframe>
                </div>
                <div class="iframe-wrapper">
                    <h3>👤 User 2 (Bob)</h3>
                    <iframe src="frontend.html" id="user2"></iframe>
                </div>
            `;
            log('Opened 2 test instances. Wait for them to load, then join the same room.');
        }

        function runAIChatTest() {
            log('🧪 Starting AI Chat Reopen Test...');
            log('Manual steps:');
            log('1. In User 1: Close the AI chat sidebar using the X button');
            log('2. Verify that a blue "Open AI Chat" button appears in the toolbar');
            log('3. Click the button to reopen the AI chat');
            log('4. Repeat for User 2');
            updateTestStatus('AI Chat Reopen Button Visibility', 'pending');
        }

        function runVideoCallTest() {
            log('🧪 Starting Comprehensive WebRTC Video Call Test...');
            log('CRITICAL FIXES IMPLEMENTED:');
            log('✅ Fixed bidirectional WebRTC communication');
            log('✅ Added Join Call functionality');
            log('✅ Fixed WebRTC offer handling for non-active users');
            log('✅ Enhanced media stream debugging');
            log('');
            log('TEST STEPS:');
            log('1. User 1: Join a room (e.g., "test-room")');
            log('2. User 2: Join the SAME room');
            log('3. User 1: Click "Start Video Meeting" - should see own video');
            log('4. User 2: Should see notification + "Join Call" button');
            log('5. User 2: Click "Join Call" - should see both videos');
            log('6. Verify BIDIRECTIONAL audio/video between both users');
            log('7. Test mute/unmute controls on both sides');
            log('8. Test one user leaving and rejoining');
            log('9. End call and verify cleanup');
            log('');
            log('DEBUGGING: Open browser console (F12) to see WebRTC logs');
            updateTestStatus('Video Call Start Notification', 'pending');
            updateTestStatus('Camera Auto-Start Prevention', 'pending');
            updateTestStatus('Multi-User Video/Audio', 'pending');
            updateTestStatus('WebRTC Connection Establishment', 'pending');
        }

        function updateTestStatus(testName, status) {
            const checklist = document.getElementById('testChecklist');
            const statusElements = checklist.querySelectorAll('.status');
            
            statusElements.forEach(element => {
                if (element.textContent.includes(testName)) {
                    element.className = `status ${status}`;
                    const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '🔄';
                    element.textContent = `${icon} ${testName}`;
                }
            });
        }

        function clearResults() {
            testResults.textContent = 'Results cleared. Ready for new tests...\n';
            // Reset all test statuses to pending
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach(element => {
                element.className = 'status pending';
                const testName = element.textContent.replace(/^[🔄✅❌]\s/, '');
                element.textContent = `🔄 ${testName}`;
            });
        }

        // Auto-start instructions
        window.onload = function() {
            log('🚀 Video Call Testing Suite Loaded');
            log('Follow the test instructions above to verify all functionality.');
            log('Make sure the collaboration server is running on port 8765.');
        };
    </script>
</body>
</html>

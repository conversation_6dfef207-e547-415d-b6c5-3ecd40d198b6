<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .button.primary { background-color: #007bff; color: white; }
        .button.success { background-color: #28a745; color: white; }
        .button.danger { background-color: #dc3545; color: white; }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-box {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        video {
            width: 200px;
            height: 150px;
            background-color: #000;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebRTC Debug Test</h1>
        
        <div class="test-section">
            <h3>Step 1: Media Access</h3>
            <button class="button primary" onclick="testMediaAccess()">Test Media Access</button>
            <button class="button danger" onclick="stopMedia()">Stop Media</button>
            <div class="video-container">
                <div class="video-box">
                    <h4>Local Video</h4>
                    <video id="localVideo" autoplay muted playsinline></video>
                </div>
                <div class="video-box">
                    <h4>Remote Video</h4>
                    <video id="remoteVideo" autoplay playsinline></video>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Step 2: WebRTC Connection</h3>
            <button class="button primary" onclick="createOffer()">Create Offer (Caller)</button>
            <button class="button success" onclick="createAnswer()">Create Answer (Callee)</button>
            <button class="button danger" onclick="closeConnection()">Close Connection</button>
        </div>

        <div class="test-section">
            <h3>Debug Log</h3>
            <button class="button primary" onclick="clearLog()">Clear Log</button>
            <div id="debugLog" class="log">Ready for testing...\n</div>
        </div>
    </div>

    <script>
        let localStream = null;
        let remoteStream = null;
        let peerConnection = null;
        let offer = null;
        let answer = null;

        const rtcConfiguration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = 'Log cleared...\n';
        }

        async function testMediaAccess() {
            try {
                log('🎥 Requesting media access...');
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });
                
                log(`✅ Media access granted! Stream has ${localStream.getTracks().length} tracks:`);
                localStream.getTracks().forEach(track => {
                    log(`  - ${track.kind}: enabled=${track.enabled}, readyState=${track.readyState}`);
                });

                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = localStream;
                log('📺 Local video element updated');

            } catch (error) {
                log(`❌ Media access failed: ${error.message}`);
            }
        }

        function stopMedia() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
                document.getElementById('localVideo').srcObject = null;
                log('🛑 Media stopped');
            }
        }

        async function createOffer() {
            if (!localStream) {
                log('❌ No local stream available. Test media access first.');
                return;
            }

            try {
                log('🔗 Creating peer connection...');
                peerConnection = new RTCPeerConnection(rtcConfiguration);

                // Add event listeners
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        log(`🧊 ICE candidate: ${event.candidate.type}`);
                    } else {
                        log('🧊 ICE gathering complete');
                    }
                };

                peerConnection.ontrack = (event) => {
                    log(`📡 Received remote track: ${event.track.kind}`);
                    remoteStream = event.streams[0];
                    document.getElementById('remoteVideo').srcObject = remoteStream;
                    log('📺 Remote video element updated');
                };

                peerConnection.onconnectionstatechange = () => {
                    log(`🔗 Connection state: ${peerConnection.connectionState}`);
                };

                // Add local tracks
                log('➕ Adding local tracks to peer connection...');
                localStream.getTracks().forEach(track => {
                    log(`  Adding ${track.kind} track`);
                    peerConnection.addTrack(track, localStream);
                });

                // Create offer
                log('📤 Creating offer...');
                offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                log('✅ Offer created and local description set');
                log('📋 Copy this offer to the other browser:');
                log(JSON.stringify(offer));

            } catch (error) {
                log(`❌ Error creating offer: ${error.message}`);
            }
        }

        async function createAnswer() {
            if (!localStream) {
                log('❌ No local stream available. Test media access first.');
                return;
            }

            const offerText = prompt('Paste the offer from the other browser:');
            if (!offerText) return;

            try {
                const receivedOffer = JSON.parse(offerText);
                log('📥 Received offer, creating peer connection...');

                peerConnection = new RTCPeerConnection(rtcConfiguration);

                // Add event listeners
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        log(`🧊 ICE candidate: ${event.candidate.type}`);
                    } else {
                        log('🧊 ICE gathering complete');
                    }
                };

                peerConnection.ontrack = (event) => {
                    log(`📡 Received remote track: ${event.track.kind}`);
                    remoteStream = event.streams[0];
                    document.getElementById('remoteVideo').srcObject = remoteStream;
                    log('📺 Remote video element updated');
                };

                peerConnection.onconnectionstatechange = () => {
                    log(`🔗 Connection state: ${peerConnection.connectionState}`);
                };

                // Add local tracks
                log('➕ Adding local tracks to peer connection...');
                localStream.getTracks().forEach(track => {
                    log(`  Adding ${track.kind} track`);
                    peerConnection.addTrack(track, localStream);
                });

                // Set remote description and create answer
                await peerConnection.setRemoteDescription(receivedOffer);
                log('✅ Remote description set');

                answer = await peerConnection.createAnswer();
                await peerConnection.setLocalDescription(answer);
                log('✅ Answer created and local description set');
                log('📋 Copy this answer back to the first browser:');
                log(JSON.stringify(answer));

            } catch (error) {
                log(`❌ Error creating answer: ${error.message}`);
            }
        }

        function closeConnection() {
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
                document.getElementById('remoteVideo').srcObject = null;
                log('🔌 Connection closed');
            }
        }

        // Auto-start instructions
        window.onload = function() {
            log('🚀 WebRTC Debug Test Ready');
            log('Instructions:');
            log('1. Open this page in TWO browser windows');
            log('2. In BOTH windows: Click "Test Media Access"');
            log('3. In window 1: Click "Create Offer", copy the offer');
            log('4. In window 2: Click "Create Answer", paste the offer');
            log('5. Copy the answer from window 2 back to window 1');
            log('6. Both windows should show each other\'s video');
        };
    </script>
</body>
</html>

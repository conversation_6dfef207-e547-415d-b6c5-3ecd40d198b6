<!DOCTYPE html>
<html lang="en" class=""> <!-- Add 'dark' class here for default dark mode -->
<head>
 <meta charset="UTF-8">
 <meta name="viewport" content="width=device-width, initial-scale=1.0">
 <title>Flux Notebook - Modern UI</title>
 <script src="https://cdn.tailwindcss.com"></script>
 <script>
 tailwind.config = {
 darkMode: 'class', // or 'media'
 theme: {
 extend: {
 animation: {
 'fade-in': 'fadeIn 0.3s ease-out',
 'slide-up': 'slideUp 0.3s ease-out',
 },
 keyframes: {
 fadeIn: {
 '0%': { opacity: 0 },
 '100%': { opacity: 1 },
 },
 slideUp: {
 '0%': { transform: 'translateY(10px)', opacity: 0 },
 '100%': { transform: 'translateY(0)', opacity: 1 },
 }
 }
 }
 }
 }
 </script>
 <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
 <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
 
 <!-- MathJax Configuration (same as before) -->
 <script>
 window.MathJax = {
 tex: { inlineMath: [['$', '$'], ['\\(', '\\)']], displayMath: [['$$', '$$'], ['\\[', '\\]']], processEscapes: true, packages: {'[+]': ['mhchem']} },
 loader: { load: ['[tex]/mhchem'] },
 svg: { fontCache: 'global' },
 startup: { ready: () => { MathJax.startup.defaultReady(); } }
 };
 </script>
 <script type="text/javascript" id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

 <style>
 /* Custom scrollbar for a modern feel */
 ::-webkit-scrollbar { width: 8px; height: 8px; }
 ::-webkit-scrollbar-track { background: transparent; }
 ::-webkit-scrollbar-thumb { background: #d1d5db; border-radius: 4px; }
 .dark ::-webkit-scrollbar-thumb { background: #374151; }
 ::-webkit-scrollbar-thumb:hover { background: #9ca3af; }
 .dark ::-webkit-scrollbar-thumb:hover { background: #4b5563; }

 /* Style for active tool button */
 .active-tool {
 background-color: #3b82f6 !important;
 color: white !important;
 }
 .dark .active-tool {
 background-color: #3b82f6 !important;
 color: white !important;
 }

 /* Ensure canvas selection box is visible in dark mode */
 .dark .canvas-container .upper-canvas .selection {
 border-color: rgba(59, 130, 246, 0.7) !important;
 background-color: rgba(59, 130, 246, 0.2) !important;
 }

 #magnifier-preview {
 backdrop-filter: blur(4px);
 -webkit-backdrop-filter: blur(4px);
 }

 /* Modern button styling */
 .toolbar-button {
 padding: 0.5rem;
 border-radius: 0.5rem;
 background-color: #f3f4f6;
 color: #000000;
 transition: all 0.2s;
 }
 .toolbar-button:hover {
 background-color: #e5e7eb;
 }
 .dark .toolbar-button {
 background-color: #1f2937;
 color: #ffffff;
 }
 .dark .toolbar-button:hover {
 background-color: #374151;
 }
 </style>
</head>
<body class="bg-white dark:bg-black text-black dark:text-white font-sans antialiased overflow-hidden">
 <div id="app-container" class="flex h-screen">

 <!-- Main Content Area (Toolbar + Canvas) -->
 <div id="main-content" class="flex-1 flex flex-col overflow-hidden">
 <header id="toolbar-wrapper" class="bg-white dark:bg-black shadow-sm print:hidden relative z-20">
 <div class="px-4 py-2 flex items-center justify-between h-14">
 <div class="flex items-center space-x-3">
 <svg class="h-7 w-7 text-black dark:text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
 <path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z" fill="currentColor"/>
 </svg>
 <span class="text-xl font-semibold text-black dark:text-white">Flux Notebook</span>
 <span id="auth-status" class="text-xs px-2.5 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 rounded-full font-medium">Status: Not Logged In</span>

 <!-- Collaboration Status -->
 <div id="collaboration-status" class="flex items-center space-x-2 px-2.5 py-1 rounded-full bg-gray-100 dark:bg-gray-800">
 <div id="connection-indicator" class="w-2 h-2 rounded-full bg-red-500"></div>
 <span id="connection-text" class="text-xs text-gray-600 dark:text-gray-300">Offline</span>
 <span id="room-info" class="text-xs text-gray-500 dark:text-gray-400 hidden"></span>
 </div>

 <!-- Active Users -->
 <div id="active-users" class="flex items-center space-x-1 hidden">
 <svg class="w-4 h-4 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
 </svg>
 <span id="user-count" class="text-xs text-black dark:text-white">0</span>
 </div>
 </div>
 <button id="toggle-toolbar-content-btn" title="Toggle Toolbar" class="p-2 rounded-lg text-gray-500 hover:text-black hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-all duration-200">
 <!-- Chevron Up/Down Icon will be inserted by JS -->
 <svg id="toolbar-chevron-icon" class="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
 </button>
 </div>
 <div id="toolbar-content" class="px-4 pb-3 pt-1 border-t border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out overflow-hidden max-h-[500px]"> <!-- max-h for animation -->
 <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-4 gap-y-3">
 
 <!-- Drawing Tools Group -->
 <div class="tool-group-wrapper">
 <p class="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1.5 uppercase tracking-wider">Drawing</p>
 <div class="flex flex-wrap gap-1.5 items-center">
 <button id="pen-tool" title="Pen" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
 </svg>
 </button>
 <button id="highlighter-rect-tool" title="Highlight Rectangle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16v12H4z"/>
 </svg>
 </button>
 <button id="highlighter-circle-tool" title="Highlight Circle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <circle cx="12" cy="12" r="8" stroke-width="2"/>
 </svg>
 </button>
 <button id="select-tool" title="Select Objects" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.243m-4.243 4.243L8 8m0 8l8-8"/>
 </svg>
 </button>
 <button id="erase-selected" title="Erase Selected Objects" class="p-2 rounded-lg bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-600 dark:text-orange-400 transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
 </svg>
 </button>
 <button id="erase-canvas" title="Erase Whole Canvas" class="p-2 rounded-lg bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400 transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
 </svg>
 </button>
 <input type="color" id="color-picker" title="Color" class="w-8 h-8 p-0.5 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer appearance-none bg-transparent">
 <div class="flex items-center space-x-1 px-2 py-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
 <svg class="w-4 h-4 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
 </svg>
 <input type="range" id="brush-size" title="Brush Size" min="1" max="50" value="5" class="w-20 h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500">
 </div>

 <button id="debug-delete" title="Debug Delete" class="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
 </svg>
 </button>

 </div>
 </div>

 <!-- AI Tools Group -->
 <div class="tool-group-wrapper">
 <p class="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1.5 uppercase tracking-wider">AI</p>
 <div class="flex flex-wrap gap-1.5">
 <button id="ask-ai-canvas" title="Ask AI (Screenshot Canvas)" class="px-4 py-2 rounded-lg bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-700 transition-all duration-200 flex items-center">
 <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
 </svg>
 Ask AI
 </button>
 </div>
 </div>

 <!-- View Group -->
 <div class="tool-group-wrapper">
 <p class="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1.5 uppercase tracking-wider">View</p>
 <div class="flex flex-wrap gap-1.5 items-center">
 <button id="toggle-high-contrast" title="Toggle Dark Mode" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
 </svg>
 </button>
 <button id="zoom-in" title="Zoom In" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"/>
 </svg>
 </button>
 <button id="zoom-out" title="Zoom Out" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7"/>
 </svg>
 </button>
 <button id="toggle-magnifier" title="Toggle Magnifier" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
 </svg>
 </button>
 <div class="flex items-center space-x-1 px-2 py-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
 <svg class="w-4 h-4 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
 </svg>
 <input type="range" id="font-sizer" title="Chat Font Size" min="10" max="24" value="14" step="1" class="w-20 h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500">
 </div>
 </div>
 </div>
 
 <!-- Collaboration Group -->
 <div class="tool-group-wrapper">
 <p class="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1.5 uppercase tracking-wider">Collaborate</p>
 <div class="flex flex-wrap gap-1.5">
 <button id="create-room-btn" title="Create Room" class="px-4 py-2 rounded-lg bg-green-500 hover:bg-green-600 text-white dark:bg-green-600 dark:hover:bg-green-700 transition-all duration-200 flex items-center">
 <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
 </svg>
 Create
 </button>
 <button id="join-room-btn" title="Join Room" class="px-4 py-2 rounded-lg bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-700 transition-all duration-200 flex items-center">
 <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
 </svg>
 Join
 </button>
 <button id="leave-room-btn" title="Leave Room" class="px-4 py-2 rounded-lg bg-red-500 hover:bg-red-600 text-white dark:bg-red-600 dark:hover:bg-red-700 transition-all duration-200 flex items-center hidden">
 <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
 </svg>
 Leave
 </button>
 </div>
 </div>

 <!-- Media Controls Group -->
 <div class="tool-group-wrapper">
 <p class="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1.5 uppercase tracking-wider">Media</p>
 <div class="flex flex-wrap gap-1.5">
 <button id="toggle-video" title="Toggle Video" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
 </svg>
 </button>
 <button id="toggle-audio" title="Toggle Audio" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
 </svg>
 </button>
 </div>
 </div>

 <!-- Session Group -->
 <div class="tool-group-wrapper">
 <p class="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1.5 uppercase tracking-wider">Session</p>
 <div class="flex flex-wrap gap-1.5">
 <button id="export-pdf" title="Export as PDF" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-black dark:text-white transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
 </svg>
 </button>

 <button id="start-meeting-btn" title="Start Video Meeting" class="p-2 rounded-lg bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-700 transition-all duration-200">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
 </svg>
 </button>

 <button id="open-ai-chat-toolbar-btn" title="Open AI Chat" class="p-2 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-all duration-200 hidden">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
 </svg>
 </button>
 </div>
 </div>
 </div>
 </div>
 </header>

 <!-- Google Meet Style Video Bar -->
 <div id="video-meeting-bar" class="bg-gray-100 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 transition-all duration-300 hidden">
 <div class="flex items-center justify-between px-4 py-2">
 <!-- Left side: Meeting info -->
 <div class="flex items-center space-x-4">
 <div class="flex items-center space-x-2">
 <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
 <span class="text-black dark:text-white text-sm font-medium">Meeting</span>
 <span id="meeting-duration" class="text-gray-500 dark:text-gray-400 text-sm">00:00</span>
 </div>
 </div>

 <!-- Center: Video participants -->
 <div id="video-participants-container" class="flex items-center space-x-2 flex-1 justify-center max-w-4xl overflow-x-auto">
 <!-- Video tiles will be added here -->
 </div>

 <!-- Right side: Meeting controls -->
 <div class="flex items-center space-x-2">
 <button id="toggle-video-btn" class="p-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-all duration-200" title="Toggle Video">
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
 </svg>
 </button>
 <button id="toggle-audio-btn" class="p-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-all duration-200" title="Toggle Audio">
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
 </svg>
 </button>
 <button id="end-meeting-btn" class="p-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all duration-200" title="End Meeting">
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
 </svg>
 </button>
 <button id="open-ai-chat-btn" class="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-all duration-200 hidden" title="Open AI Chat">
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
 </svg>
 </button>
 </div>
 </div>
 </div>

 <main id="canvas-section" class="flex-1 relative min-h-0">
 <div id="canvas-parent" class="absolute inset-0 overflow-auto bg-white dark:bg-black">
 <canvas id="main-canvas"></canvas>
 </div>
 <div id="magnifier-preview" class="w-36 h-36 border-2 border-gray-400 dark:border-gray-500 rounded-full pointer-events-none hidden absolute bg-no-repeat z-50 shadow-xl bg-white/50 dark:bg-gray-800/50"></div>
 </main>
 </div>

 <!-- Right Sidebar for AI Chat -->
 <aside id="ai-chat-sidebar" class="bg-white dark:bg-black border-l border-gray-200 dark:border-gray-700 flex flex-col p-0 print:hidden transition-all duration-300 ease-in-out relative" style="width: 384px; min-width: 300px; max-width: 600px;">
 <!-- Resize Handle -->
 <div id="resize-handle" class="absolute left-0 top-0 w-1 h-full bg-gray-300 dark:bg-gray-600 hover:bg-blue-500 dark:hover:bg-blue-400 cursor-col-resize transition-colors opacity-0 hover:opacity-100 z-10"></div>

 <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center h-14">
 <h3 class="text-lg font-semibold text-black dark:text-white">AI Assistant</h3>
 <button id="toggle-chat-sidebar-btn" class="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200" title="Close AI Panel">
 <svg class="w-4 h-4 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
 </svg>
 </button>
 </div>
 <div id="chat-history" class="flex-grow p-4 space-y-4 overflow-y-auto text-sm">
 <!-- Chat messages will be appended here by JS -->
 <div class="ai-message animate-fade-in"><strong>AI: </strong>Welcome. Draw on the canvas. Use "Ask AI" button to send canvas screenshot for questions. Example: $\frac{d}{dx} x^2 = 2x$ or $$E=mc^2$$ or chemical formula $\ce{H2O + CO2 <=> H2CO3}$</div>
 </div>
 <div id="chat-input-area" class="p-4 border-t border-gray-200 dark:border-gray-700">
 <div class="flex items-center space-x-2">
 <input type="text" id="chat-input" placeholder="Ask AI..." class="flex-grow p-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none bg-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 text-sm transition-all duration-200">
 <button id="send-button" class="bg-blue-500 hover:bg-blue-600 text-white p-2.5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800">
 <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
 </svg>
 </button>
 </div>
 </div>
 </aside>
 </div>

<script>
document.addEventListener('DOMContentLoaded', () => {
 console.log("DOM fully loaded. Initializing Modern UI script...");

 const fabricCanvas = new fabric.Canvas('main-canvas', {
 width: 3000, height: 2000, isDrawingMode: false,
 backgroundColor: document.documentElement.classList.contains('dark') ? '#000000' : '#ffffff', // black or white
 selection: true,
 });
 console.log("Fabric canvas initialized.");

 // Enhanced WebRTC + WebSocket for real-time canvas synchronization
 let dataChannels = new Map(); // userId -> dataChannel
 let pendingOperations = new Map(); // operationId -> operation details
 let operationQueue = []; // Queue for reliable delivery
 let isProcessingRemoteOperation = false; // Flag to prevent loops
 let lastOperationTime = 0; // For throttling
 let isCreatingPath = false; // Flag to prevent duplicate path/object events
 let recentPathIds = new Set(); // Track recently created path IDs

 // --- DOM Elements ---
 const chatHistory = document.getElementById('chat-history');
 const chatInput = document.getElementById('chat-input');
 const sendButton = document.getElementById('send-button');
 const colorPicker = document.getElementById('color-picker');
 const brushSizeSlider = document.getElementById('brush-size');
 const penToolButton = document.getElementById('pen-tool');
 const highlighterRectButton = document.getElementById('highlighter-rect-tool');
 const highlighterCircleButton = document.getElementById('highlighter-circle-tool');
 const selectToolButton = document.getElementById('select-tool');
 const eraseSelectedButton = document.getElementById('erase-selected');
 const eraseCanvasButton = document.getElementById('erase-canvas');
 const debugDeleteButton = document.getElementById('debug-delete');
 const askAiCanvasButton = document.getElementById('ask-ai-canvas');
 const toggleHighContrastButton = document.getElementById('toggle-high-contrast');
 const zoomInButton = document.getElementById('zoom-in');
 const zoomOutButton = document.getElementById('zoom-out');
 const toggleMagnifierButton = document.getElementById('toggle-magnifier');
 const fontSizer = document.getElementById('font-sizer');
 const exportPdfButton = document.getElementById('export-pdf');
 const startMeetingBtn = document.getElementById('start-meeting-btn');
 const authStatusEl = document.getElementById('auth-status');
 const canvasParent = document.getElementById('canvas-parent'); // For magnifier
 const mainCanvasEl = document.getElementById('main-canvas'); 
 const magnifierPreview = document.getElementById('magnifier-preview');
 const toggleToolbarContentBtn = document.getElementById('toggle-toolbar-content-btn');
 const toolbarContent = document.getElementById('toolbar-content');
 const toolbarChevronIcon = document.getElementById('toolbar-chevron-icon');
 
 const allToolButtons = [penToolButton, highlighterRectButton, highlighterCircleButton, selectToolButton];

 // --- Collaboration Elements ---
 const collaborationStatus = document.getElementById('collaboration-status');
 const connectionIndicator = document.getElementById('connection-indicator');
 const connectionText = document.getElementById('connection-text');
 const roomInfo = document.getElementById('room-info');
 const activeUsers = document.getElementById('active-users');
 const userCount = document.getElementById('user-count');
 const createRoomBtn = document.getElementById('create-room-btn');
 const joinRoomBtn = document.getElementById('join-room-btn');
 const leaveRoomBtn = document.getElementById('leave-room-btn');
 const createRoomModal = document.getElementById('create-room-modal');
 const joinRoomModal = document.getElementById('join-room-modal');
 const userCursors = document.getElementById('user-cursors');

 // --- Video/Audio Elements ---
 const toggleVideoBtn = document.getElementById('toggle-video-btn');
 const toggleAudioBtn = document.getElementById('toggle-audio-btn');
 const videoMeetingBar = document.getElementById('video-meeting-bar');
 const videoParticipantsContainer = document.getElementById('video-participants-container');
 const meetingDuration = document.getElementById('meeting-duration');
 const endMeetingBtn = document.getElementById('end-meeting-btn');

 // Debug: Check if elements are found
 console.log('Video/Audio elements found:');
 console.log('toggleVideoBtn:', toggleVideoBtn);
 console.log('toggleAudioBtn:', toggleAudioBtn);
 console.log('videoMeetingBar:', videoMeetingBar);
 console.log('videoParticipantsContainer:', videoParticipantsContainer);
 console.log('endMeetingBtn:', endMeetingBtn);

 // Test if buttons are clickable
 if (toggleVideoBtn) {
 console.log('Video button found and will be made clickable');
 } else {
 console.error('Video button NOT found!');
 }

 if (toggleAudioBtn) {
 console.log('Audio button found and will be made clickable');
 } else {
 console.error('Audio button NOT found!');
 }

 // Legacy elements for compatibility
 const videoCallArea = videoMeetingBar;
 const videoGrid = videoParticipantsContainer;
 const callDuration = meetingDuration;

 // --- Resizable AI Panel Elements ---
 const aiChatSidebar = document.getElementById('ai-chat-sidebar');
 const resizeHandle = document.getElementById('resize-handle');
 const toggleChatSidebarBtn = document.getElementById('toggle-chat-sidebar-btn');
 const openAiChatBtn = document.getElementById('open-ai-chat-btn');
 const openAiChatToolbarBtn = document.getElementById('open-ai-chat-toolbar-btn');

 // --- Initial State & Variables ---
 let pendingImageForChat = null;
 let currentTool = 'pen';
 let isDrawingHighlight = false;
 let startPoint = null;
 let currentHighlightShape = null;
 let currentZoom = 1;
 let magnifierActive = false;
 const magnifierZoomLevel = 3;

 // --- Collaboration Variables ---
 let collaborationSocket = null;
 let currentUserId = null;
 let currentRoomId = null;
 let isConnected = false;
 let roomUsers = new Map();
 let isReceivingRemoteEvent = false; // Legacy flag - kept for compatibility

 // --- Video/Audio Variables ---
 let localStream = null;
 let peerConnections = new Map();
 let isVideoEnabled = false;
 let isAudioEnabled = false;
 let isCallActive = false;
 let callStartTime = null;
 let callTimer = null;
 let isVideoMinimized = false;

 // WebRTC Configuration
 const rtcConfiguration = {
 iceServers: [
 { urls: 'stun:stun.l.google.com:19302' },
 { urls: 'stun:stun1.l.google.com:19302' }
 ]
 };

 // --- Dark Mode Persistence ---
 if (localStorage.getItem('darkMode') === 'true' || 
 (!('darkMode' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
 document.documentElement.classList.add('dark');
 } else {
 document.documentElement.classList.remove('dark');
 }
 fabricCanvas.backgroundColor = document.documentElement.classList.contains('dark') ? '#000000' : '#ffffff'; // black or white
 fabricCanvas.renderAll();


 // --- Toolbar Collapse ---
 let isToolbarContentCollapsed = localStorage.getItem('toolbarCollapsed') === 'true';
 function updateToolbarView() {
 if (isToolbarContentCollapsed) {
 toolbarContent.style.maxHeight = '0px';
 toolbarContent.classList.add('!py-0', '!border-t-0');
 toolbarChevronIcon.style.transform = 'rotate(180deg)';
 } else {
 toolbarContent.style.maxHeight = "230px"; // <-- Changed to 230px
 toolbarContent.classList.remove('!py-0', '!border-t-0');
 toolbarChevronIcon.style.transform = 'rotate(0deg)';
 }
}
 // Initial call, after a small delay to allow scrollHeight to be calculated correctly
 setTimeout(updateToolbarView, 100); 


 toggleToolbarContentBtn.addEventListener('click', () => {
 isToolbarContentCollapsed = !isToolbarContentCollapsed;
 localStorage.setItem('toolbarCollapsed', isToolbarContentCollapsed);
 updateToolbarView();
 // Recalculate canvas offset if needed, though Fabric usually handles this well with relative positioning
 setTimeout(() => fabricCanvas.calcOffset(), 350); // after animation
 });
 // Adjust max-height on resize if toolbar is open
 window.addEventListener('resize', () => {
 if (!isToolbarContentCollapsed) {
 toolbarContent.style.maxHeight = toolbarContent.scrollHeight + "px";
 }
 });

 // --- Brush Settings ---
 if (!fabricCanvas.freeDrawingBrush) {
 fabricCanvas.freeDrawingBrush = new fabric.PencilBrush(fabricCanvas);
 }
 fabricCanvas.freeDrawingBrush.color = colorPicker.value;
 fabricCanvas.freeDrawingBrush.width = parseInt(brushSizeSlider.value, 10);

 // --- Tool Selection Visuals ---
 function setActiveToolButton(selectedButton) {
 allToolButtons.forEach(btn => btn.classList.remove('active-tool'));
 if (selectedButton) {
 selectedButton.classList.add('active-tool');
 }
 }
 setActiveToolButton(penToolButton); // Default active tool

 // --- Event Listeners (Adapted for new UI) ---
 colorPicker.addEventListener('input', (e) => { // 'input' for live update
 const newColor = e.target.value;
 if (fabricCanvas.freeDrawingBrush) fabricCanvas.freeDrawingBrush.color = newColor;
 const activeObject = fabricCanvas.getActiveObject();
 if (activeObject) {
 if (activeObject.isAIHighlight) {
 activeObject.set('fill', fabric.Color.fromHex(newColor).setAlpha(0.3).toRgba());
 activeObject.set('stroke', fabric.Color.fromHex(newColor).setAlpha(0.7).toRgba());
 } else {
 activeObject.set('fill', newColor); activeObject.set('stroke', newColor);
 }
 fabricCanvas.renderAll();
 }
 });

 brushSizeSlider.addEventListener('input', (e) => {
 const size = parseInt(e.target.value, 10);
 if (fabricCanvas.freeDrawingBrush) fabricCanvas.freeDrawingBrush.width = size;
 const activeObject = fabricCanvas.getActiveObject();
 if (activeObject && activeObject.strokeWidth !== undefined) {
 activeObject.set('strokeWidth', size);
 fabricCanvas.renderAll();
 }
 });
 
 penToolButton.addEventListener('click', () => {
 currentTool = 'pen'; fabricCanvas.isDrawingMode = true;
 fabricCanvas.freeDrawingBrush = new fabric.PencilBrush(fabricCanvas);
 fabricCanvas.freeDrawingBrush.color = colorPicker.value;
 fabricCanvas.freeDrawingBrush.width = parseInt(brushSizeSlider.value, 10);
 setActiveToolButton(penToolButton);
 });

 const startHighlightDrawing = (shapeType, buttonToActivate) => {
 currentTool = shapeType; fabricCanvas.isDrawingMode = false; isDrawingHighlight = true;
 fabricCanvas.selection = false; fabricCanvas.discardActiveObject(); fabricCanvas.renderAll();
 setActiveToolButton(buttonToActivate);
 };
 highlighterRectButton.addEventListener('click', () => startHighlightDrawing('highlighter-rect', highlighterRectButton));
 highlighterCircleButton.addEventListener('click', () => startHighlightDrawing('highlighter-circle', highlighterCircleButton));

 selectToolButton.addEventListener('click', () => {
 currentTool = 'select';
 fabricCanvas.isDrawingMode = false;
 fabricCanvas.selection = true;
 setActiveToolButton(selectToolButton);
 // Removed system message - too noisy
 });

 eraseSelectedButton.addEventListener('click', () => {
 const activeObjects = fabricCanvas.getActiveObjects();
 if (activeObjects.length === 0) {
 addChatMessage('System', 'No objects selected. Please select objects first using the Select tool.');
 return;
 }

 if (confirm(`Are you sure you want to erase ${activeObjects.length} selected object(s)? This action cannot be undone.`)) {
 eraseSelectedObjects(activeObjects);
 // Removed system message - too noisy
 }
 });

 eraseCanvasButton.addEventListener('click', () => {
 if (confirm('Are you sure you want to erase the entire canvas? This action cannot be undone and will affect all users.')) {
 eraseWholeCanvas();
 // Removed system message - too noisy
 }
 });

 debugDeleteButton.addEventListener('click', () => {
 console.log('=== DEBUG DELETE BUTTON CLICKED ===');
 console.log('Current room ID:', currentRoomId);
 console.log('WebSocket state:', collaborationSocket?.readyState);
 console.log('isProcessingRemoteOperation:', isProcessingRemoteOperation);
 console.log('All objects on canvas:', fabricCanvas.getObjects().length);
 console.log('Active objects:', fabricCanvas.getActiveObjects().length);

 const allObjects = fabricCanvas.getObjects();
 console.log('All objects details:', allObjects.map(obj => ({
 id: obj.id,
 type: obj.type,
 hasId: !!obj.id
 })));

 const activeObjects = fabricCanvas.getActiveObjects();
 if (activeObjects.length > 0) {
 console.log('Active objects details:', activeObjects.map(obj => ({
 id: obj.id,
 type: obj.type,
 hasId: !!obj.id
 })));

 // Test sending a removal event for the first active object
 const testObj = activeObjects[0];
 if (!testObj.id) {
 testObj.id = 'debug_' + Date.now();
 console.log('Generated debug ID:', testObj.id);
 }

 console.log('Testing sendCanvasEvent for object:', testObj.id);
 const success = sendCanvasEvent('object_removed', {
 object_id: testObj.id
 });

 if (success) {
 console.log('✅ Debug event sent successfully');
 } else {
 console.error('❌ Debug event failed to send');
 }
 } else {
 console.log('No active objects to test with');
 }

 addChatMessage('System', 'Debug info logged to console');
 });



 askAiCanvasButton.addEventListener('click', () => {
 addChatMessage('System', 'Capturing canvas for AI...');
 console.log('🎨 Starting canvas capture for AI...');

 const originalBackgroundColor = fabricCanvas.backgroundColor;
 // Ensure a non-transparent background for the screenshot
 if (fabricCanvas.backgroundColor === 'transparent' || !fabricCanvas.backgroundColor) {
 fabricCanvas.backgroundColor = document.documentElement.classList.contains('dark') ? '#000000' : '#FFFFFF';
 }
 fabricCanvas.renderAll(); // Render with temporary background if changed
 const dataURL = fabricCanvas.toDataURL({ format: 'png', quality: 0.9 });

 console.log('📸 Canvas captured, data URL length:', dataURL.length);
 console.log('📸 Data URL preview:', dataURL.substring(0, 100) + '...');

 // Restore original background if it was temporarily changed
 if (fabricCanvas.backgroundColor !== originalBackgroundColor) {
 fabricCanvas.backgroundColor = originalBackgroundColor;
 fabricCanvas.renderAll();
 }

 if (dataURL && dataURL.length > 100) {
 addChatMessage('User', `<img src="${dataURL}" alt="Full canvas screenshot" class="max-w-full rounded-md border border-slate-300 dark:border-slate-600" />`, true);
 pendingImageForChat = dataURL;
 addChatMessage('AI', 'Canvas captured! Type your question below and press Send.');
 console.log('✅ Canvas image ready for AI analysis');
 chatInput.focus();
 } else {
 console.error('❌ Failed to capture canvas - invalid data URL');
 addChatMessage('System', 'Failed to capture canvas.');
 }
 });

 toggleHighContrastButton.addEventListener('click', () => {
 document.documentElement.classList.toggle('dark');
 const isDarkMode = document.documentElement.classList.contains('dark');
 localStorage.setItem('darkMode', isDarkMode);
 fabricCanvas.backgroundColor = isDarkMode ? '#000000' : '#ffffff'; // black or white
 fabricCanvas.renderAll();
 if (typeof MathJax !== 'undefined' && MathJax.typesetPromise) {
 MathJax.typesetPromise([chatHistory]).catch((err) => console.error('MathJax re-typesetting error on contrast toggle:', err));
 }
 // Removed system message - too noisy
 });
 
 zoomInButton.addEventListener('click', () => {
 currentZoom = Math.min(currentZoom * 1.2, 10); fabricCanvas.setZoom(currentZoom);
 fabricCanvas.setWidth(3000 * currentZoom); fabricCanvas.setHeight(2000 * currentZoom); 
 fabricCanvas.renderAll(); canvasParent.scrollLeft = (canvasParent.scrollWidth - canvasParent.clientWidth) / 2; canvasParent.scrollTop = (canvasParent.scrollHeight - canvasParent.clientHeight) / 2;
 });
 zoomOutButton.addEventListener('click', () => {
 currentZoom = Math.max(currentZoom / 1.2, 0.1); fabricCanvas.setZoom(currentZoom);
 fabricCanvas.setWidth(3000 * currentZoom); fabricCanvas.setHeight(2000 * currentZoom); 
 fabricCanvas.renderAll(); canvasParent.scrollLeft = (canvasParent.scrollWidth - canvasParent.clientWidth) / 2; canvasParent.scrollTop = (canvasParent.scrollHeight - canvasParent.clientHeight) / 2;
 });
 
 toggleMagnifierButton.addEventListener('click', () => {
 magnifierActive = !magnifierActive;
 magnifierPreview.style.display = magnifierActive ? 'block' : 'none';
 // Removed system message - too noisy
 });
 
 fontSizer.addEventListener('input', (e) => {
 chatHistory.style.fontSize = e.target.value + 'px';
 });
 
 exportPdfButton.addEventListener('click', () => {
 const { jsPDF } = window.jspdf;
 const imgData = fabricCanvas.toDataURL({ format: 'png', quality: 1.0 });
 const pdf = new jsPDF({
 orientation: fabricCanvas.width > fabricCanvas.height ? 'l' : 'p',
 unit: 'px',
 format: [fabricCanvas.width, fabricCanvas.height]
 });
 pdf.addImage(imgData, 'PNG', 0, 0, fabricCanvas.width, fabricCanvas.height);
 pdf.save("notebook_export.pdf");
 // Removed system message - too noisy
 });

 startMeetingBtn.addEventListener('click', async () => {
 console.log('Start Meeting button clicked');
 if (!currentRoomId) {
 addChatMessage('System', 'Please join a room first to start a video meeting');
 return;
 }

 if (isCallActive) {
 addChatMessage('System', 'Meeting is already active');
 return;
 }

 await startVideoCall();
 });
 


 // Enhanced synchronized erase functions using new operation system
 function eraseSelectedObjects(objects) {
 if (!objects || objects.length === 0) return;

 console.log('🗑️ Erasing', objects.length, 'selected objects');

 // Ensure all objects have IDs for synchronization
 objects.forEach((obj, index) => {
 if (!obj.id) {
 obj.id = generateObjectId();
 console.log('Generated ID for object without ID:', obj.id);
 }
 });

 // Send removal operations for each object
 objects.forEach((obj, index) => {
 console.log(`� Sending object removal for object ${index + 1}/${objects.length}:`, obj.id);

 // Use new canvas operation system
 sendCanvasOperation('object_removed', {
 object_id: obj.id,
 object_type: obj.type,
 removal_method: 'selected_erase'
 });
 });

 // Remove objects locally with flag to prevent duplicate events
 const originalFlag = isProcessingRemoteOperation;
 isProcessingRemoteOperation = true;

 objects.forEach((obj) => {
 fabricCanvas.remove(obj);
 });

 fabricCanvas.discardActiveObject().renderAll();

 // Restore flag after a short delay
 setTimeout(() => {
 isProcessingRemoteOperation = originalFlag;
 }, 50);

 console.log('✅ Selected objects erased and synchronized');
 }

 function eraseWholeCanvas() {
 console.log('🗑️ Erasing whole canvas');

 // Clear the canvas locally
 fabricCanvas.clear();
 fabricCanvas.backgroundColor = document.documentElement.classList.contains('dark') ? '#000000' : '#ffffff';
 fabricCanvas.renderAll();
 pendingImageForChat = null;

 // Send canvas cleared operation
 if (currentRoomId) {
 sendCanvasOperation('canvas_cleared', {
 background: fabricCanvas.backgroundColor,
 cleared_by: currentUserId,
 timestamp: Date.now()
 });
 }

 console.log('✅ Whole canvas erased and synchronized');
 }

 // Enhanced keyboard shortcuts for erase functionality
 document.addEventListener('keydown', function(e) {
 if ((e.key === 'Delete' || e.key === 'Backspace') && document.activeElement !== chatInput && fabricCanvas.getActiveObjects().length > 0) {
 e.preventDefault(); // Prevent default browser behavior

 console.log('🔥 Delete/Backspace key triggered');
 const activeObjects = fabricCanvas.getActiveObjects();

 if (activeObjects.length > 0) {
 console.log('Active objects for removal:', activeObjects.map(obj => ({
 id: obj.id,
 type: obj.type,
 hasId: !!obj.id
 })));

 // Use new synchronized erase system
 eraseSelectedObjects(activeObjects);
 }
 }
 });
 
 mainCanvasEl.parentElement.addEventListener('contextmenu', (e) => e.preventDefault());

 // --- Fabric Mouse Events (Highlights) ---
 fabricCanvas.on('mouse:down', (o) => {
 if (!isDrawingHighlight) return;
 const pointer = fabricCanvas.getPointer(o.e);
 startPoint = pointer;
 let color = fabric.Color.fromHex(colorPicker.value).setAlpha(0.3).toRgba();
 let borderColor = fabric.Color.fromHex(colorPicker.value).setAlpha(0.7).toRgba();

 if (currentTool === 'highlighter-rect') {
 currentHighlightShape = new fabric.Rect({
 left: startPoint.x, top: startPoint.y, width: 0, height: 0,
 fill: color, stroke: borderColor, strokeWidth: 2,
 selectable: true, evented: true, isAIHighlight: true,
 id: Date.now() + '_' + Math.random().toString(36).substr(2, 9)
 });
 } else if (currentTool === 'highlighter-circle') {
 currentHighlightShape = new fabric.Ellipse({
 left: startPoint.x, top: startPoint.y, rx: 0, ry: 0,
 fill: color, stroke: borderColor, strokeWidth: 2,
 selectable: true, evented: true, isAIHighlight: true,
 originX: 'left', originY: 'top',
 id: Date.now() + '_' + Math.random().toString(36).substr(2, 9)
 });
 }
 if (currentHighlightShape) fabricCanvas.add(currentHighlightShape);
 });

 fabricCanvas.on('mouse:move', (o) => {
 if (magnifierActive) { // Magnifier logic moved here to be inside canvas boundary
 const canvasRect = mainCanvasEl.getBoundingClientRect();
 // Use o.e (original event) for pageX/pageY relative to document
 // Use fabricCanvas.getPointer(o.e) for canvas-relative coordinates
 const pointer = fabricCanvas.getPointer(o.e); // x,y on unzoomed canvas
 
 magnifierPreview.style.left = (o.e.pageX - magnifierPreview.offsetWidth / 2) + 'px';
 magnifierPreview.style.top = (o.e.pageY - magnifierPreview.offsetHeight / 2) + 'px';
 
 magnifierPreview.style.backgroundImage = `url(${fabricCanvas.toDataURL()})`; // Use fabricCanvas.toDataURL for current state
 magnifierPreview.style.backgroundRepeat = 'no-repeat';
 // Adjust backgroundPosition based on canvas-relative coords and current canvas zoom
 const bgX = (pointer.x * currentZoom * magnifierZoomLevel) - (magnifierPreview.offsetWidth / 2);
 const bgY = (pointer.y * currentZoom * magnifierZoomLevel) - (magnifierPreview.offsetHeight / 2);
 magnifierPreview.style.backgroundPosition = `-${bgX}px -${bgY}px`;
 magnifierPreview.style.backgroundSize = `${fabricCanvas.width * magnifierZoomLevel}px ${fabricCanvas.height * magnifierZoomLevel}px`;
 magnifierPreview.style.display = 'block';
 }

 if (!isDrawingHighlight || !currentHighlightShape || !startPoint) return;
 const pointer = fabricCanvas.getPointer(o.e);
 if (currentTool === 'highlighter-rect') {
 currentHighlightShape.set({
 width: Math.abs(pointer.x - startPoint.x),
 height: Math.abs(pointer.y - startPoint.y),
 left: Math.min(pointer.x, startPoint.x),
 top: Math.min(pointer.y, startPoint.y),
 });
 } else if (currentTool === 'highlighter-circle') {
 currentHighlightShape.set({
 rx: Math.abs(pointer.x - startPoint.x) / 2,
 ry: Math.abs(pointer.y - startPoint.y) / 2,
 left: Math.min(pointer.x, startPoint.x),
 top: Math.min(pointer.y, startPoint.y),
 });
 if (currentHighlightShape.rx < 0) currentHighlightShape.rx *= -1; 
 if (currentHighlightShape.ry < 0) currentHighlightShape.ry *= -1;
 }
 fabricCanvas.renderAll();
 });

 fabricCanvas.on('mouse:up', () => {
 if (isDrawingHighlight) {
 isDrawingHighlight = false;
 if(currentHighlightShape) {
 currentHighlightShape.setCoords(); 
 fabricCanvas.setActiveObject(currentHighlightShape);
 }
 currentHighlightShape = null;
 startPoint = null;
 fabricCanvas.selection = true; 
 }
 });
 
 // Hide magnifier when mouse leaves canvas area
 canvasParent.addEventListener('mouseleave', () => { if (magnifierActive) magnifierPreview.style.display = 'none'; });
 // canvasParent.addEventListener('mouseenter', () => { if (magnifierActive) magnifierPreview.style.display = 'block'; }); // Handled by mouse:move on canvas

 // --- Chat Functions ---
 function addChatMessage(sender, message, isHtml = false) {
 const msgDiv = document.createElement('div');
 msgDiv.classList.add('chat-message', 'break-words', 'p-2.5', 'rounded-lg', 'shadow-sm', 'animate-slide-up');
 
 let senderPrefix = '';
 if (sender.toLowerCase() === 'user') {
 msgDiv.classList.add('bg-indigo-500', 'text-white', 'self-end', 'ml-auto', 'max-w-[85%]');
 senderPrefix = 'You: ';
 } else if (sender.toLowerCase() === 'ai') {
 msgDiv.classList.add('bg-slate-200', 'dark:bg-slate-700', 'text-slate-800', 'dark:text-slate-100', 'self-start', 'mr-auto', 'max-w-[85%]');
 senderPrefix = 'AI: ';
 } else { // System
 msgDiv.classList.add('bg-transparent', 'text-slate-500', 'dark:text-slate-400', 'text-xs', 'italic', 'text-center', 'w-full', 'shadow-none', 'py-1');
 senderPrefix = ''; // System messages don't need a "System:" prefix visually if centered italic.
 }

 const senderStrong = document.createElement('strong');
 senderStrong.textContent = senderPrefix;
 
 const messageContentSpan = document.createElement('span');
 if (isHtml) {
 messageContentSpan.innerHTML = message;
 } else {
 // Apply Tailwind classes to pre/code if not using HTML for message content
 const codeBlockRegex = /(```python\n[\s\S]*?```|```[\s\S]*?```)/g;
 const parts = message.split(codeBlockRegex);
 parts.forEach(part => {
 if (codeBlockRegex.test(part)) {
 const pre = document.createElement('pre');
 pre.className = "bg-slate-800 dark:bg-black text-slate-100 dark:text-slate-200 p-3 my-2 rounded-md overflow-x-auto text-xs";
 const code = document.createElement('code');
 code.textContent = part.replace(/^```python\n|^```|```$/g, '');
 pre.appendChild(code); 
 messageContentSpan.appendChild(pre);
 } else {
 messageContentSpan.appendChild(document.createTextNode(part));
 }
 });
 }
 if(senderPrefix) msgDiv.appendChild(senderStrong); // Only add prefix if it exists
 msgDiv.appendChild(messageContentSpan);
 chatHistory.appendChild(msgDiv);

 if ( (sender.toLowerCase() === 'ai' || sender.toLowerCase() === 'user') && 
 !isHtml && typeof MathJax !== 'undefined' && MathJax.typesetPromise &&
 (message.includes('$') || message.includes('\\') || message.includes('^') || message.includes('_'))) {
 MathJax.typesetPromise([messageContentSpan]).catch((err) => console.error('MathJax typesetting error:', err));
 }
 chatHistory.scrollTop = chatHistory.scrollHeight;
 }

 sendButton.addEventListener('click', sendUserMessage);
 chatInput.addEventListener('keypress', (e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); sendUserMessage(); }});

 function sendUserMessage() {
 const message = chatInput.value.trim();
 if (!message && !pendingImageForChat) {
 addChatMessage('System', "Type a message or capture canvas with 'Ask AI' first."); return;
 }
 let messageForAI = message;
 if (pendingImageForChat) {
 if (!message) messageForAI = "Analyze this image and solve any mathematical problems shown. Use LaTeX formatting for math.";
 // User message with image is already added by askAiCanvasButton
 if (message) addChatMessage('User', message);
 } else {
 if (message) addChatMessage('User', message);
 }
 let promptToBackend = messageForAI;
 if (!promptToBackend.toLowerCase().includes("latex")) {
 promptToBackend += " (Format ALL math, symbols, and equations using LaTeX: $inline$ or $$display$$)";
 }

 // Store the image before clearing it
 const imageToSend = pendingImageForChat;
 chatInput.value = '';
 pendingImageForChat = null;

 // Send to AI with the stored image
 sendToGemini(imageToSend, promptToBackend);
 }

 // Use current domain for API calls
const GEMINI_BACKEND_URL = window.location.origin;
 async function sendToGemini(imageDataURL, promptText) {
 addChatMessage('AI', '<i>Processing... please wait.</i>', true);
 try {
 const effectivePrompt = promptText || "Analyze the provided content. Format all math using LaTeX.";
 const payload = { message: effectivePrompt };

 if (imageDataURL) {
 console.log('Sending image with prompt to AI...');
 const parts = imageDataURL.split(',');
 if (parts.length === 2) {
 payload.image_data = parts[1];
 console.log('Image data prepared, size:', parts[1].length, 'characters');
 } else {
 console.error("Invalid Data URL format:", imageDataURL);
 addChatMessage('System', 'Error: Could not properly prepare image data for AI.');
 return;
 }
 } else {
 console.log('Sending text-only prompt to AI...');
 }

 console.log('Payload being sent:', { ...payload, image_data: payload.image_data ? `[IMAGE_DATA_${payload.image_data.length}_CHARS]` : undefined });
 console.log('🌐 Sending request to:', `${GEMINI_BACKEND_URL}/api/chat`);

 const response = await fetch(`${GEMINI_BACKEND_URL}/api/chat`, {
 method: 'POST',
 headers: { 'Content-Type': 'application/json' },
 body: JSON.stringify(payload)
 });

 console.log('📡 Response status:', response.status);
 console.log('📡 Response ok:', response.ok);
 
 // Remove "Processing..." message
 const processingMessages = Array.from(chatHistory.querySelectorAll('.ai-message i')).filter(i => i.textContent.includes('Processing...'));
 processingMessages.forEach(pm => pm.closest('.chat-message')?.remove());

 if (!response.ok) { const errorData = await response.json(); throw new Error(errorData.error || `HTTP error! status: ${response.status}`); }
 const data = await response.json();
 addChatMessage('AI', data.response || "No response from AI.");
 // Note: Python code execution removed for now since backend doesn't support it
 } catch (error) {
 const processingMessages = Array.from(chatHistory.querySelectorAll('.ai-message i')).filter(i => i.textContent.includes('Processing...'));
 processingMessages.forEach(pm => pm.closest('.chat-message')?.remove());
 console.error('Error interacting with Gemini:', error);
 addChatMessage('AI', `Sorry, an error occurred: ${error.message}`);
 }
 }

 // Python execution removed - not supported in current backend

 // --- Collaboration Functions ---
 function updateConnectionStatus(connected, roomId = null) {
 isConnected = connected;
 currentRoomId = roomId;

 if (connected) {
 connectionIndicator.className = 'w-2 h-2 rounded-full bg-green-500';
 connectionText.textContent = roomId ? 'Connected' : 'Online';
 if (roomId) {
 roomInfo.textContent = `Room: ${roomId}`;
 roomInfo.classList.remove('hidden');
 activeUsers.classList.remove('hidden');
 leaveRoomBtn.classList.remove('hidden');
 createRoomBtn.classList.add('hidden');
 joinRoomBtn.classList.add('hidden');
 } else {
 roomInfo.classList.add('hidden');
 activeUsers.classList.add('hidden');
 leaveRoomBtn.classList.add('hidden');
 createRoomBtn.classList.remove('hidden');
 joinRoomBtn.classList.remove('hidden');
 }
 } else {
 connectionIndicator.className = 'w-2 h-2 rounded-full bg-red-500';
 connectionText.textContent = 'Offline';
 roomInfo.classList.add('hidden');
 activeUsers.classList.add('hidden');
 leaveRoomBtn.classList.add('hidden');
 createRoomBtn.classList.remove('hidden');
 joinRoomBtn.classList.remove('hidden');
 }
 }

 function connectToCollaborationServer() {
 if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
 return;
 }

 try {
 // Determine WebSocket URL based on environment
 let wsUrl;
 const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

 // Use the same domain and port as the main app, but with /ws endpoint
 wsUrl = `${protocol}//${window.location.host}/ws`;

 console.log('Connecting to WebSocket at:', wsUrl);
 collaborationSocket = new WebSocket(wsUrl);

 collaborationSocket.onopen = () => {
 console.log('Connected to collaboration server');
 updateConnectionStatus(true);

 // Get or ask for user name
 let userName = localStorage.getItem('userName');
 if (!userName || userName === 'Anonymous') {
 userName = prompt('Welcome! Please enter your display name:') || 'Anonymous';
 localStorage.setItem('userName', userName);
 }

 console.log('Registering user with name:', userName);
 collaborationSocket.send(JSON.stringify({
 type: 'register',
 name: userName
 }));
 };

 collaborationSocket.onmessage = (event) => {
 try {
 const data = JSON.parse(event.data);
 handleCollaborationMessage(data);
 } catch (error) {
 console.error('Error parsing collaboration message:', error);
 }
 };

 collaborationSocket.onclose = () => {
 console.log('Disconnected from collaboration server');
 updateConnectionStatus(false);
 currentUserId = null;
 currentRoomId = null;
 roomUsers.clear();
 clearUserCursors();

 // Attempt to reconnect after 3 seconds
 setTimeout(connectToCollaborationServer, 3000);
 };

 collaborationSocket.onerror = (error) => {
 console.error('Collaboration WebSocket error:', error);
 };

 } catch (error) {
 console.error('Failed to connect to collaboration server:', error);
 updateConnectionStatus(false);
 }
 }

 function handleCollaborationMessage(data) {
 switch (data.type) {
 case 'registered':
 currentUserId = data.user_id;
 console.log('Registered with user ID:', currentUserId);
 console.log('User registered with name:', localStorage.getItem('userName') || 'Anonymous');
 // Removed system message for connection - too noisy
 break;

 case 'room_created':
 if (data.success) {
 updateConnectionStatus(true, data.room_id);
 addChatMessage('System', `Room created: ${data.room_id}`);
 // Copy room ID to clipboard
 navigator.clipboard.writeText(data.room_id).then(() => {
 addChatMessage('System', 'Room ID copied to clipboard');
 });

 // Add the room creator to roomUsers
 if (currentUserId) {
 const currentUserName = localStorage.getItem('userName') || 'Anonymous';
 roomUsers.set(currentUserId, { id: currentUserId, name: currentUserName });
 console.log('Added room creator to roomUsers:', currentUserId, currentUserName);
 updateUserCount();
 }
 }
 break;

 case 'room_joined':
 if (data.success) {
 updateConnectionStatus(true, data.room_id);
 addChatMessage('System', `Joined room: ${data.room_id}`);

 // Update room users so everyone can see each other's cursors
 if (data.users) {
 console.log('Updating room users:', data.users);
 updateRoomUsers(data.users);
 }
 } else {
 addChatMessage('System', 'Failed to join room');
 }
 break;

 case 'room_left':
 updateConnectionStatus(true, null); // Connected but not in a room
 addChatMessage('System', 'Left room');
 roomUsers.clear();
 clearUserCursors();
 currentRoomId = null;
 break;

 case 'canvas_state':
 loadCanvasState(data.state);
 updateRoomUsers(data.users);
 updateConnectionStatus(true, data.room.id);
 break;

 case 'canvas_event':
 handleRemoteCanvasEvent(data.event, data.user_id);
 break;

 case 'user_joined':
 addChatMessage('System', `${data.user.name} joined the room`);
 updateRoomUsers([data.user]);

 // If we're in an active call, establish peer connection with new user
 if (isCallActive && localStream) {
 createPeerConnection(data.user.id, true); // true = we are the initiator
 }
 break;

 case 'user_left':
 const leftUser = roomUsers.get(data.user_id);
 if (leftUser) {
 addChatMessage('System', `${leftUser.name} left the room`);
 roomUsers.delete(data.user_id);
 removeUserCursor(data.user_id);

 // Clean up peer connection
 const peerConnection = peerConnections.get(data.user_id);
 if (peerConnection) {
 peerConnection.close();
 peerConnections.delete(data.user_id);
 }

 // Remove video/audio element if exists
 const videoContainer = document.getElementById(`video-container-${data.user_id}`);
 const audioContainer = document.getElementById(`audio-container-${data.user_id}`);
 if (videoContainer) videoContainer.remove();
 if (audioContainer) audioContainer.remove();
 updateUserCount();
 }
 break;

 case 'video_call_started':
 if (data.user_id !== currentUserId) {
 const userName = data.user_name || 'Unknown User';
 addChatMessage('System', `${userName} started video call`);

 // Show meeting bar if not already visible
 if (!isCallActive) {
 videoMeetingBar.classList.remove('hidden');
 }

 // Create audio-only element for the user (they can enable video later)
 createAudioOnlyElement(data.user_id, userName);

 // If we're already in a call, establish peer connection with the new user
 if (isCallActive && localStream) {
 createPeerConnection(data.user_id, false); // false = we are not the initiator
 }
 }
 break;

 case 'video_call_ended':
 if (data.user_id !== currentUserId) {
 const user = roomUsers.get(data.user_id);
 const userName = user ? user.name : 'Unknown User';
 addChatMessage('System', `${userName} ended video call`);

 // Remove their video/audio element
 const videoContainer = document.getElementById(`video-container-${data.user_id}`);
 const audioContainer = document.getElementById(`audio-container-${data.user_id}`);
 if (videoContainer) videoContainer.remove();
 if (audioContainer) audioContainer.remove();
 }
 break;

 case 'media_status':
 if (data.user_id !== currentUserId) {
 updateRemoteUserMediaStatus(data.user_id, data.video_enabled, data.audio_enabled);

 // If user enabled video and only has audio element, create video element
 if (data.video_enabled) {
 const audioContainer = document.getElementById(`audio-container-${data.user_id}`);
 if (audioContainer) {
 const user = roomUsers.get(data.user_id);
 const userName = user ? user.name : 'Unknown User';
 audioContainer.remove();
 createVideoElement(data.user_id, userName, false);
 }
 }
 // If user disabled video and has video element, create audio-only element
 else {
 const videoContainer = document.getElementById(`video-container-${data.user_id}`);
 if (videoContainer) {
 const user = roomUsers.get(data.user_id);
 const userName = user ? user.name : 'Unknown User';
 videoContainer.remove();
 createAudioOnlyElement(data.user_id, userName);
 }
 }
 }
 break;

 case 'cursor_move':
 updateUserCursor(data.user_id, data.x, data.y);
 break;

 // WebRTC signaling messages
 case 'webrtc_offer':
 handleWebRTCOffer(data);
 break;

 case 'webrtc_answer':
 handleWebRTCAnswer(data);
 break;

 case 'webrtc_ice_candidate':
 handleWebRTCIceCandidate(data);
 break;

 case 'name_updated':
 if (data.success) {
 console.log('Name updated successfully to:', data.new_name);
 } else {
 console.warn('Failed to update name');
 }
 break;

 case 'user_name_updated':
 // Update the user in our local cache
 const updatedUser = roomUsers.get(data.user_id);
 if (updatedUser) {
 updatedUser.name = data.new_name;
 roomUsers.set(data.user_id, updatedUser);

 // Update cursor display if it exists
 const cursor = document.getElementById(`cursor-${data.user_id}`);
 if (cursor) {
 const nameSpan = cursor.querySelector('span');
 if (nameSpan) {
 nameSpan.textContent = data.new_name;
 }
 }

 addChatMessage('System', `${data.old_name} changed their name to ${data.new_name}`);
 }
 break;
 }
 }

 function updateRoomUsers(users) {
 console.log('updateRoomUsers called with:', users);
 users.forEach(user => {
 console.log('Adding user to roomUsers:', user.id, user.name);
 roomUsers.set(user.id, user);
 });
 console.log('Total users in room:', roomUsers.size);
 updateUserCount();
 }

 function updateUserCount() {
 userCount.textContent = roomUsers.size;
 }

 function loadCanvasState(state) {
 isProcessingRemoteOperation = true;

 fabricCanvas.clear();
 fabricCanvas.backgroundColor = state.background || '#ffffff';

 if (state.objects && state.objects.length > 0) {
 state.objects.forEach(objData => {
 fabric.util.enlivenObjects([objData], (objects) => {
 objects.forEach(obj => {
 fabricCanvas.add(obj);
 });
 fabricCanvas.renderAll();
 });
 });
 }

 fabricCanvas.renderAll();
 isProcessingRemoteOperation = false;
 }

 function handleRemoteCanvasEvent(event, userId) {
 console.log('📥 Received canvas event:', event, 'from user:', userId);
 handleRemoteCanvasOperation(event, userId, 'websocket');
 }

 // Comprehensive remote canvas operation handler
 function handleRemoteCanvasOperation(operation, userId, source = 'websocket') {
 console.log(`📥 handleRemoteCanvasOperation called:`, {
 operation,
 userId,
 source,
 operationType: operation?.type,
 hasData: !!operation?.data
 });

 if (!operation || !operation.type) {
 console.error('❌ Invalid canvas operation received:', operation);
 return;
 }

 // Prevent processing our own operations
 if (operation.user_id === currentUserId) {
 console.log('⏭️ Skipping own operation from user:', operation.user_id);
 return;
 }

 console.log(`📥 Processing ${source} operation:`, operation.type, 'from user:', userId);

 isProcessingRemoteOperation = true;

 try {
 // Handle both new nested structure (operation.data) and old flat structure
 const eventData = operation.data || operation;

 switch (operation.type) {
 case 'selection_created':
 case 'selection_updated':
 handleRemoteSelection(eventData, userId);
 break;

 case 'selection_cleared':
 handleRemoteSelectionCleared(eventData, userId);
 break;

 case 'path_created':
 handleRemotePathCreated(eventData);
 break;

 case 'object_added':
 handleRemoteObjectAdded(eventData);
 break;

 case 'object_modified':
 handleRemoteObjectModified(eventData);
 break;

 case 'object_moving':
 handleRemoteObjectMoving(eventData);
 break;

 case 'object_scaling':
 handleRemoteObjectScaling(eventData);
 break;

 case 'object_rotating':
 handleRemoteObjectRotating(eventData);
 break;

 case 'object_removed':
 handleRemoteObjectRemoval(eventData);
 break;

 case 'canvas_cleared':
 handleRemoteCanvasCleared(eventData);
 break;

 default:
 console.warn('Unknown operation type:', operation.type);
 }
 } catch (error) {
 console.error('Error processing remote operation:', error);
 } finally {
 // Restore flag after a short delay to ensure all related events are processed
 setTimeout(() => {
 isProcessingRemoteOperation = false;
 }, 10);
 }
 }

 // Remote operation handlers
 function handleRemoteSelection(data, userId) {
 console.log('👆 Remote selection from user:', userId, data);
 // Visual indication of remote user selection (optional)
 // Could show selection indicators for other users
 }

 function handleRemoteSelectionCleared(data, userId) {
 console.log('👆 Remote selection cleared from user:', userId);
 // Clear visual indicators for remote user selection
 }

 function handleRemotePathCreated(data) {
 console.log('🎨 handleRemotePathCreated called with data:', data);
 if (data.path) {
 console.log('🎨 Creating path from object:', data.path);

 // Check if path already exists to avoid duplicates
 const existingPath = fabricCanvas.getObjects().find(obj => obj.id === data.object_id);
 if (existingPath) {
 console.log('⏭️ Path already exists, skipping:', data.object_id);
 return;
 }

 fabric.Path.fromObject(data.path, (path) => {
 // Ensure the path has an ID
 path.id = data.object_id || generateObjectId();
 fabricCanvas.add(path);
 fabricCanvas.renderAll();
 console.log('✅ Remote path created successfully:', path.id);
 });
 } else {
 console.warn('❌ No path data in handleRemotePathCreated');
 }
 }

 function handleRemoteObjectAdded(data) {
 console.log('🎨 handleRemoteObjectAdded called with data:', data);
 if (data.object) {
 // Skip paths since they're handled by handleRemotePathCreated
 if (data.object.type === 'path') {
 console.log('⏭️ Skipping path in handleRemoteObjectAdded (handled by handleRemotePathCreated)');
 return;
 }

 // Check if object already exists to avoid duplicates
 const existingObj = fabricCanvas.getObjects().find(obj => obj.id === data.object_id);
 if (existingObj) {
 console.log('⏭️ Object already exists, skipping:', data.object_id);
 return;
 }

 console.log('🎨 Creating object from data:', data.object);
 fabric.util.enlivenObjects([data.object], (objects) => {
 const obj = objects[0];
 if (obj) {
 // Ensure the object has an ID
 obj.id = data.object_id || generateObjectId();
 fabricCanvas.add(obj);
 fabricCanvas.renderAll();
 console.log('✅ Remote object added successfully:', obj.type, obj.id);
 }
 });
 } else {
 console.warn('❌ No object data in handleRemoteObjectAdded');
 }
 }

 function handleRemoteObjectModified(data) {
 if (data.object_id) {
 const existingObj = fabricCanvas.getObjects().find(obj => obj.id === data.object_id);
 if (existingObj && data.object) {
 // Update object properties
 fabric.util.enlivenObjects([data.object], (objects) => {
 const newObj = objects[0];
 if (newObj) {
 // Preserve the ID and update properties
 newObj.id = data.object_id;
 fabricCanvas.remove(existingObj);
 fabricCanvas.add(newObj);
 fabricCanvas.renderAll();
 console.log('✅ Remote object modified:', data.object_id);
 }
 });
 }
 }
 }

 function handleRemoteObjectMoving(data) {
 if (data.object_id && data.position) {
 const obj = fabricCanvas.getObjects().find(o => o.id === data.object_id);
 if (obj) {
 obj.set({
 left: data.position.left,
 top: data.position.top
 });
 obj.setCoords();
 fabricCanvas.renderAll();
 }
 }
 }

 function handleRemoteObjectScaling(data) {
 if (data.object_id && data.scale) {
 const obj = fabricCanvas.getObjects().find(o => o.id === data.object_id);
 if (obj) {
 obj.set({
 scaleX: data.scale.scaleX,
 scaleY: data.scale.scaleY
 });
 obj.setCoords();
 fabricCanvas.renderAll();
 }
 }
 }

 function handleRemoteObjectRotating(data) {
 if (data.object_id && data.angle !== undefined) {
 const obj = fabricCanvas.getObjects().find(o => o.id === data.object_id);
 if (obj) {
 obj.set('angle', data.angle);
 obj.setCoords();
 fabricCanvas.renderAll();
 }
 }
 }

 function handleRemoteObjectRemoval(data) {
 if (!data.object_id) {
 console.error('❌ Received object_removed without object_id');
 return;
 }

 const objToRemove = fabricCanvas.getObjects().find(obj => obj.id === data.object_id);
 if (objToRemove) {
 fabricCanvas.remove(objToRemove);
 fabricCanvas.renderAll();
 console.log('✅ Remote object removed:', data.object_id);
 } else {
 console.warn('❌ Object not found for removal:', data.object_id);
 }
 }

 function handleRemoteCanvasCleared(data) {
 fabricCanvas.clear();
 fabricCanvas.backgroundColor = data.background || (document.documentElement.classList.contains('dark') ? '#0f172a' : '#ffffff');
 fabricCanvas.renderAll();
 console.log('✅ Remote canvas cleared');
 }

 // Enhanced WebRTC Data Channel message handler
 function handleWebRTCMessage(message, userId) {
 try {
 const data = JSON.parse(message);
 console.log('📡 Received WebRTC message from', userId, ':', data);

 if (data.type === 'canvas_operation') {
 handleRemoteCanvasOperation(data.operation, userId, 'webrtc');
 }
 } catch (error) {
 console.error('❌ Error parsing WebRTC message:', error);
 }
 }

 // Generate unique object ID
 function generateObjectId() {
 return `${currentUserId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
 }

 // Enhanced canvas operation sender with WebRTC + WebSocket fallback
 function sendCanvasOperation(operationType, data = {}, priority = 'normal') {
 if (!currentRoomId || isProcessingRemoteOperation) {
 return false;
 }

 const operation = {
 id: generateObjectId(),
 type: operationType,
 timestamp: Date.now(),
 priority,
 data,
 user_id: currentUserId
 };

 console.log(`🚀 Sending ${priority} canvas operation:`, operation);

 let sentViaWebRTC = false;

 // For high-priority operations (real-time movement), try WebRTC first
 if (priority === 'high' && dataChannels.size > 0) {
 dataChannels.forEach((channel, userId) => {
 if (channel.readyState === 'open') {
 try {
 channel.send(JSON.stringify({
 type: 'canvas_operation',
 operation
 }));
 sentViaWebRTC = true;
 console.log(`✅ Sent via WebRTC to user ${userId}`);
 } catch (error) {
 console.warn(`⚠️ WebRTC send failed to user ${userId}:`, error);
 }
 }
 });
 }

 // Always send via WebSocket as primary or backup
 const websocketSuccess = sendCanvasEvent(operationType, data);

 if (sentViaWebRTC || websocketSuccess) {
 // Store operation for potential retry
 pendingOperations.set(operation.id, operation);

 // Clean up old operations
 setTimeout(() => {
 pendingOperations.delete(operation.id);
 }, 5000);

 return true;
 }

 return false;
 }

 // Setup WebRTC data channel for a peer connection
 function setupDataChannel(peerConnection, userId) {
 try {
 const dataChannel = peerConnection.createDataChannel('objectOperations', {
 ordered: true,
 maxRetransmits: 3
 });

 dataChannel.onopen = () => {
 console.log('✅ Data channel opened for user:', userId);
 dataChannels.set(userId, dataChannel);
 };

 dataChannel.onclose = () => {
 console.log('❌ Data channel closed for user:', userId);
 dataChannels.delete(userId);
 };

 dataChannel.onmessage = (event) => {
 handleWebRTCMessage(event.data, userId);
 };

 dataChannel.onerror = (error) => {
 console.error('❌ Data channel error for user', userId, ':', error);
 };

 // Handle incoming data channels
 peerConnection.ondatachannel = (event) => {
 const channel = event.channel;
 console.log('📡 Received data channel from user:', userId);

 channel.onopen = () => {
 console.log('✅ Incoming data channel opened for user:', userId);
 dataChannels.set(userId, channel);
 };

 channel.onmessage = (event) => {
 handleWebRTCMessage(event.data, userId);
 };

 channel.onclose = () => {
 console.log('❌ Incoming data channel closed for user:', userId);
 dataChannels.delete(userId);
 };
 };

 return dataChannel;
 } catch (error) {
 console.error('❌ Error setting up data channel:', error);
 return null;
 }
 }



 function sendCanvasEvent(eventType, data = {}) {
 console.log('🚀 sendCanvasEvent called:', {
 eventType,
 data,
 hasSocket: !!collaborationSocket,
 socketState: collaborationSocket?.readyState,
 currentRoomId,
 isProcessingRemoteOperation
 });

 if (!collaborationSocket) {
 console.error('❌ No collaboration socket available');
 return false;
 }

 if (collaborationSocket.readyState !== WebSocket.OPEN) {
 console.error('❌ WebSocket not open, state:', collaborationSocket.readyState);
 return false;
 }

 if (!currentRoomId) {
 console.error('❌ Not in a room, cannot send canvas event');
 return false;
 }

 if (isProcessingRemoteOperation) {
 console.warn('⚠️ Currently processing remote operation, skipping send to prevent loop');
 return false;
 }

 const eventData = {
 type: 'canvas_event',
 event: {
 type: eventType,
 timestamp: Date.now(),
 ...data
 }
 };

 console.log('✅ Sending canvas event:', eventData);
 try {
 collaborationSocket.send(JSON.stringify(eventData));
 console.log('✅ Canvas event sent successfully');
 return true;
 } catch (error) {
 console.error('❌ Error sending canvas event:', error);
 return false;
 }
 }

 function updateUserCursor(userId, x, y) {
 console.log('Updating cursor for user:', userId, 'at position:', x, y);
 const user = roomUsers.get(userId);
 if (!user) {
 console.log('User not found in roomUsers:', userId);
 return;
 }

 let cursor = document.getElementById(`cursor-${userId}`);
 if (!cursor) {
 console.log('Creating new cursor for user:', userId);
 cursor = document.createElement('div');
 cursor.id = `cursor-${userId}`;
 cursor.className = 'absolute pointer-events-none z-50 transition-all duration-100';
 cursor.innerHTML = `
 <div class="flex items-center space-x-1">
 <div class="w-4 h-4 bg-indigo-500 rounded-full border-2 border-white shadow-lg"></div>
 <span class="text-xs bg-indigo-500 text-white px-2 py-1 rounded shadow-lg">${user.name}</span>
 </div>
 `;
 userCursors.appendChild(cursor);
 }

 const canvasRect = mainCanvasEl.getBoundingClientRect();
 const left = canvasRect.left + x * currentZoom;
 const top = canvasRect.top + y * currentZoom;
 console.log('Setting cursor position:', left, top);
 cursor.style.left = left + 'px';
 cursor.style.top = top + 'px';
 }

 function removeUserCursor(userId) {
 const cursor = document.getElementById(`cursor-${userId}`);
 if (cursor) {
 cursor.remove();
 }
 }

 function clearUserCursors() {
 userCursors.innerHTML = '';
 }

 function sendCursorPosition(x, y) {
 if (!collaborationSocket || !currentRoomId) {
 console.log('Cursor not sent - socket:', !!collaborationSocket, 'room:', currentRoomId);
 return;
 }

 console.log('Sending cursor position:', x, y);
 collaborationSocket.send(JSON.stringify({
 type: 'cursor_move',
 x: x,
 y: y
 }));
 }

 // --- Video/Audio Functions ---
 async function initializeMedia() {
 try {
 console.log('Requesting media access...');
 localStream = await navigator.mediaDevices.getUserMedia({
 video: true,
 audio: true
 });
 console.log('Media access granted, stream:', localStream);

 isVideoEnabled = true;
 isAudioEnabled = true;
 updateMediaButtons();

 // Removed system message - too noisy
 return true;
 } catch (error) {
 console.error('Error accessing media devices:', error);
 addChatMessage('System', 'Failed to access camera/microphone: ' + error.message);
 return false;
 }
 }

 function createVideoElement(userId, userName, isLocal = false) {
 const videoContainer = document.createElement('div');
 videoContainer.id = `video-container-${userId}`;
 videoContainer.className = 'relative bg-slate-800 rounded-lg overflow-hidden flex-shrink-0 border border-slate-300 dark:border-slate-600';
 videoContainer.style.width = '120px';
 videoContainer.style.height = '90px';

 const video = document.createElement('video');
 video.id = `video-${userId}`;
 video.className = 'w-full h-full object-cover';
 video.autoplay = true;
 video.playsInline = true;
 video.muted = isLocal; // Mute local video to prevent feedback

 const nameLabel = document.createElement('div');
 nameLabel.className = 'absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded text-center max-w-full truncate';
 nameLabel.textContent = userName;
 nameLabel.style.fontSize = '10px';

 const statusIndicators = document.createElement('div');
 statusIndicators.className = 'absolute top-1 right-1 flex space-x-1';

 // Video status indicator (smaller for compact view)
 const videoStatus = document.createElement('div');
 videoStatus.id = `video-status-${userId}`;
 videoStatus.className = 'w-3 h-3 bg-green-500 rounded-full flex items-center justify-center';
 videoStatus.innerHTML = `
 <svg class="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
 </svg>
 `;

 // Audio status indicator (smaller for compact view)
 const audioStatus = document.createElement('div');
 audioStatus.id = `audio-status-${userId}`;
 audioStatus.className = 'w-3 h-3 bg-green-500 rounded-full flex items-center justify-center';
 audioStatus.innerHTML = `
 <svg class="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
 </svg>
 `;

 statusIndicators.appendChild(videoStatus);
 statusIndicators.appendChild(audioStatus);

 videoContainer.appendChild(video);
 videoContainer.appendChild(nameLabel);
 videoContainer.appendChild(statusIndicators);

 // Add to the new video participants container instead of videoGrid
 videoParticipantsContainer.appendChild(videoContainer);

 return video;
 }

 function createAudioOnlyElement(userId, userName) {
 const audioContainer = document.createElement('div');
 audioContainer.id = `audio-container-${userId}`;
 audioContainer.className = 'relative bg-slate-700 rounded-lg overflow-hidden flex-shrink-0 border border-slate-300 dark:border-slate-600 flex items-center justify-center';
 audioContainer.style.width = '120px';
 audioContainer.style.height = '90px';

 // User avatar/initials
 const avatar = document.createElement('div');
 avatar.className = 'w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg';
 avatar.textContent = userName.charAt(0).toUpperCase();

 const nameLabel = document.createElement('div');
 nameLabel.className = 'absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded text-center max-w-full truncate';
 nameLabel.textContent = userName;
 nameLabel.style.fontSize = '10px';

 const statusIndicators = document.createElement('div');
 statusIndicators.className = 'absolute top-1 right-1 flex space-x-1';

 // Audio status indicator
 const audioStatus = document.createElement('div');
 audioStatus.id = `audio-status-${userId}`;
 audioStatus.className = 'w-3 h-3 bg-green-500 rounded-full flex items-center justify-center';
 audioStatus.innerHTML = `
 <svg class="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
 </svg>
 `;

 statusIndicators.appendChild(audioStatus);

 audioContainer.appendChild(avatar);
 audioContainer.appendChild(nameLabel);
 audioContainer.appendChild(statusIndicators);

 // Add to the video participants container
 videoParticipantsContainer.appendChild(audioContainer);

 return audioContainer;
 }

 function updateMediaButtons() {
 // Update video button - same icon, different colors
 if (isVideoEnabled) {
 toggleVideoBtn.className = 'p-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors';
 } else {
 toggleVideoBtn.className = 'p-2 bg-gray-400 hover:bg-gray-500 text-gray-200 rounded-lg transition-colors';
 }
 toggleVideoBtn.innerHTML = `
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
 </svg>
 `;

 // Update audio button - same icon, different colors
 if (isAudioEnabled) {
 toggleAudioBtn.className = 'p-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors';
 } else {
 toggleAudioBtn.className = 'p-2 bg-gray-400 hover:bg-gray-500 text-gray-200 rounded-lg transition-colors';
 }
 toggleAudioBtn.innerHTML = `
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
 </svg>
 `;

 // Update toolbar media buttons to match
 const toolbarVideoBtn = document.getElementById('toggle-video');
 const toolbarAudioBtn = document.getElementById('toggle-audio');

 if (toolbarVideoBtn) {
 if (isVideoEnabled) {
 toolbarVideoBtn.className = 'p-2 rounded-lg bg-green-500 hover:bg-green-600 text-white transition-all duration-200';
 } else {
 toolbarVideoBtn.className = 'p-2 rounded-lg bg-gray-400 hover:bg-gray-500 text-gray-200 transition-all duration-200';
 }
 }

 if (toolbarAudioBtn) {
 if (isAudioEnabled) {
 toolbarAudioBtn.className = 'p-2 rounded-lg bg-green-500 hover:bg-green-600 text-white transition-all duration-200';
 } else {
 toolbarAudioBtn.className = 'p-2 rounded-lg bg-gray-400 hover:bg-gray-500 text-gray-200 transition-all duration-200';
 }
 }

 // Update local video status indicators
 const localVideoStatus = document.getElementById(`video-status-${currentUserId || 'local'}`);
 const localAudioStatus = document.getElementById(`audio-status-${currentUserId || 'local'}`);

 if (localVideoStatus) {
 localVideoStatus.className = `w-3 h-3 ${isVideoEnabled ? 'bg-green-500' : 'bg-gray-400'} rounded-full flex items-center justify-center`;
 }

 if (localAudioStatus) {
 localAudioStatus.className = `w-3 h-3 ${isAudioEnabled ? 'bg-green-500' : 'bg-gray-400'} rounded-full flex items-center justify-center`;
 }
 }

 function updateRemoteUserMediaStatus(userId, videoEnabled, audioEnabled) {
 const videoStatus = document.getElementById(`video-status-${userId}`);
 const audioStatus = document.getElementById(`audio-status-${userId}`);

 if (videoStatus) {
 videoStatus.className = `w-3 h-3 ${videoEnabled ? 'bg-green-500' : 'bg-gray-400'} rounded-full flex items-center justify-center`;
 }

 if (audioStatus) {
 audioStatus.className = `w-3 h-3 ${audioEnabled ? 'bg-green-500' : 'bg-gray-400'} rounded-full flex items-center justify-center`;
 }
 }

 function toggleVideo() {
 if (!localStream) return;

 const videoTrack = localStream.getVideoTracks()[0];
 if (videoTrack) {
 videoTrack.enabled = !videoTrack.enabled;
 isVideoEnabled = videoTrack.enabled;
 updateMediaButtons();

 // Send video status to other users
 if (collaborationSocket && currentRoomId) {
 collaborationSocket.send(JSON.stringify({
 type: 'media_status',
 video_enabled: isVideoEnabled,
 audio_enabled: isAudioEnabled
 }));
 }

 // Removed system message - too noisy
 }
 }

 function toggleAudio() {
 if (!localStream) return;

 const audioTrack = localStream.getAudioTracks()[0];
 if (audioTrack) {
 audioTrack.enabled = !audioTrack.enabled;
 isAudioEnabled = audioTrack.enabled;
 updateMediaButtons();

 // Send audio status to other users
 if (collaborationSocket && currentRoomId) {
 collaborationSocket.send(JSON.stringify({
 type: 'media_status',
 video_enabled: isVideoEnabled,
 audio_enabled: isAudioEnabled
 }));
 }

 // Removed system message - too noisy
 }
 }

 async function startVideoCall() {
 console.log('startVideoCall called, currentRoomId:', currentRoomId);

 if (!currentRoomId) {
 addChatMessage('System', 'Please join a room first to start video call');
 console.log('No room ID, cannot start video call');
 return;
 }

 console.log('Initializing media...');
 const mediaInitialized = await initializeMedia();
 console.log('Media initialized:', mediaInitialized);
 if (!mediaInitialized) return;

 isCallActive = true;
 callStartTime = Date.now();
 console.log('Showing video meeting bar...');
 videoMeetingBar.classList.remove('hidden');

 // Create local video element
 const userName = localStorage.getItem('userName') || 'You';
 console.log('Creating local video element for user:', userName);
 const localVideo = createVideoElement(currentUserId || 'local', userName, true);
 localVideo.srcObject = localStream;

 // Start call timer
 callTimer = setInterval(updateCallDuration, 1000);

 // Notify other users about video call start
 if (collaborationSocket) {
 collaborationSocket.send(JSON.stringify({
 type: 'video_call_started',
 user_id: currentUserId,
 user_name: userName
 }));
 }

 addChatMessage('System', 'Video call started');
 console.log('Video call started successfully');

 // Initialize WebRTC connections for existing users
 roomUsers.forEach((user, userId) => {
 if (userId !== currentUserId) {
 createPeerConnection(userId, true); // true = we are the caller
 }
 });
 }

 function endVideoCall() {
 if (localStream) {
 localStream.getTracks().forEach(track => track.stop());
 localStream = null;
 }

 // Close all peer connections
 peerConnections.forEach(pc => pc.close());
 peerConnections.clear();

 isCallActive = false;
 isVideoEnabled = false;
 isAudioEnabled = false;
 callStartTime = null;

 if (callTimer) {
 clearInterval(callTimer);
 callTimer = null;
 }

 videoMeetingBar.classList.add('hidden');
 videoParticipantsContainer.innerHTML = '';
 updateMediaButtons();

 // Notify other users
 if (collaborationSocket && currentRoomId) {
 collaborationSocket.send(JSON.stringify({
 type: 'video_call_ended',
 user_id: currentUserId
 }));
 }

 addChatMessage('System', 'Video call ended');
 }

 function updateCallDuration() {
 if (!callStartTime) return;

 const duration = Math.floor((Date.now() - callStartTime) / 1000);
 const minutes = Math.floor(duration / 60);
 const seconds = duration % 60;

 callDuration.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
 }

 function minimizeVideoCall() {
 isVideoMinimized = !isVideoMinimized;

 if (isVideoMinimized) {
 videoGrid.style.maxHeight = '0px';
 videoGrid.style.overflow = 'hidden';
 minimizeVideoBtn.innerHTML = `
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 12h16"></path>
 </svg>
 `;
 } else {
 videoGrid.style.maxHeight = '200px';
 videoGrid.style.overflow = 'auto';
 minimizeVideoBtn.innerHTML = `
 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
 </svg>
 `;
 }
 }

 // --- WebRTC Functions ---
 async function createPeerConnection(userId, isInitiator = false) {
 console.log(`Creating peer connection for user ${userId}, isInitiator: ${isInitiator}`);

 const peerConnection = new RTCPeerConnection(rtcConfiguration);
 peerConnections.set(userId, peerConnection);

 // Add local stream tracks to peer connection
 if (localStream) {
 localStream.getTracks().forEach(track => {
 peerConnection.addTrack(track, localStream);
 });
 }

 // Handle incoming remote stream
 peerConnection.ontrack = (event) => {
 console.log('Received remote stream from user:', userId);
 const remoteStream = event.streams[0];
 const user = roomUsers.get(userId);
 const userName = user ? user.name : 'Unknown User';

 // Create or update video element for remote user
 let videoElement = document.getElementById(`video-${userId}`);
 if (!videoElement) {
 videoElement = createVideoElement(userId, userName, false);
 }
 videoElement.srcObject = remoteStream;
 };

 // Handle ICE candidates
 peerConnection.onicecandidate = (event) => {
 if (event.candidate && collaborationSocket) {
 collaborationSocket.send(JSON.stringify({
 type: 'webrtc_ice_candidate',
 target_user_id: userId,
 candidate: event.candidate
 }));
 }
 };

 // Handle connection state changes
 peerConnection.onconnectionstatechange = () => {
 console.log(`Peer connection state for ${userId}:`, peerConnection.connectionState);
 if (peerConnection.connectionState === 'failed') {
 console.log('Peer connection failed, attempting to restart');
 // Remove failed connection and try to reconnect
 peerConnections.delete(userId);
 setTimeout(() => {
 if (isCallActive && localStream) {
 console.log(`Attempting to reconnect to user ${userId}`);
 createPeerConnection(userId, true);
 }
 }, 2000);
 } else if (peerConnection.connectionState === 'disconnected') {
 console.log(`Peer connection disconnected for user ${userId}`);
 // Clean up video element if connection is lost
 const videoElement = document.getElementById(`video-${userId}`);
 if (videoElement) {
 videoElement.srcObject = null;
 }
 }
 };

 // If we're the initiator, create and send offer
 if (isInitiator) {
 try {
 const offer = await peerConnection.createOffer();
 await peerConnection.setLocalDescription(offer);

 if (collaborationSocket) {
 collaborationSocket.send(JSON.stringify({
 type: 'webrtc_offer',
 target_user_id: userId,
 offer: offer
 }));
 }
 } catch (error) {
 console.error('Error creating offer:', error);
 }
 }

 return peerConnection;
 }

 async function handleWebRTCOffer(data) {
 console.log('Received WebRTC offer from:', data.user_id);

 // Only handle offer if we're in a call
 if (!isCallActive || !localStream) {
 console.log('Not in a call, ignoring WebRTC offer');
 return;
 }

 const peerConnection = await createPeerConnection(data.user_id, false);

 try {
 await peerConnection.setRemoteDescription(data.offer);
 const answer = await peerConnection.createAnswer();
 await peerConnection.setLocalDescription(answer);

 if (collaborationSocket) {
 collaborationSocket.send(JSON.stringify({
 type: 'webrtc_answer',
 target_user_id: data.user_id,
 answer: answer
 }));
 }
 console.log('WebRTC answer sent to:', data.user_id);
 } catch (error) {
 console.error('Error handling WebRTC offer:', error);
 }
 }

 async function handleWebRTCAnswer(data) {
 console.log('Received WebRTC answer from:', data.user_id);

 const peerConnection = peerConnections.get(data.user_id);
 if (peerConnection) {
 try {
 await peerConnection.setRemoteDescription(data.answer);
 console.log('WebRTC answer processed for:', data.user_id);
 } catch (error) {
 console.error('Error handling WebRTC answer:', error);
 }
 } else {
 console.warn('No peer connection found for user:', data.user_id);
 }
 }

 async function handleWebRTCIceCandidate(data) {
 console.log('Received ICE candidate from:', data.user_id);

 const peerConnection = peerConnections.get(data.user_id);
 if (peerConnection) {
 try {
 await peerConnection.addIceCandidate(data.candidate);
 console.log('ICE candidate added for:', data.user_id);
 } catch (error) {
 console.error('Error adding ICE candidate:', error);
 }
 } else {
 console.warn('No peer connection found for ICE candidate from user:', data.user_id);
 }
 }

 // --- Collaboration Event Handlers ---
 createRoomBtn.addEventListener('click', () => {
 // Ask for display name first
 const userName = prompt('Enter your display name:');
 if (!userName || !userName.trim()) {
 alert('Display name is required to create a room');
 return;
 }

 // Store the name
 localStorage.setItem('userName', userName.trim());

 // Update user name on server if already connected
 if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
 collaborationSocket.send(JSON.stringify({
 type: 'update_name',
 name: userName.trim()
 }));
 }

 createRoomModal.classList.remove('hidden');
 document.getElementById('room-name-input').focus();
 });

 joinRoomBtn.addEventListener('click', () => {
 // Ask for display name first if not already stored
 const storedName = localStorage.getItem('userName');
 if (!storedName || storedName === 'Anonymous') {
 const userName = prompt('Enter your display name:');
 if (!userName || !userName.trim()) {
 alert('Display name is required to join a room');
 return;
 }

 // Store the name
 localStorage.setItem('userName', userName.trim());

 // Update user name on server if already connected
 if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
 collaborationSocket.send(JSON.stringify({
 type: 'update_name',
 name: userName.trim()
 }));
 }
 }

 joinRoomModal.classList.remove('hidden');
 // Pre-populate name field with stored name
 const currentName = localStorage.getItem('userName');
 if (currentName) {
 document.getElementById('user-name-input').value = currentName;
 }
 document.getElementById('room-id-input').focus();
 });

 leaveRoomBtn.addEventListener('click', () => {
 if (collaborationSocket && currentRoomId) {
 collaborationSocket.send(JSON.stringify({
 type: 'leave_room'
 }));
 }
 });

 // Modal event handlers
 document.getElementById('cancel-create-room').addEventListener('click', () => {
 createRoomModal.classList.add('hidden');
 });

 document.getElementById('cancel-join-room').addEventListener('click', () => {
 joinRoomModal.classList.add('hidden');
 });

 document.getElementById('confirm-create-room').addEventListener('click', () => {
 const roomName = document.getElementById('room-name-input').value;
 const maxUsers = parseInt(document.getElementById('max-users-select').value);

 // Ensure user has a proper name before creating room
 const currentName = localStorage.getItem('userName');
 if (!currentName || currentName === 'Anonymous') {
 const userName = prompt('Please enter your display name:');
 if (userName && userName.trim()) {
 localStorage.setItem('userName', userName.trim());
 // Update name on server
 if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
 collaborationSocket.send(JSON.stringify({
 type: 'update_name',
 name: userName.trim()
 }));
 }
 } else {
 alert('Display name is required to create a room');
 return;
 }
 }

 if (collaborationSocket) {
 // Capture current canvas state before creating room
 const currentCanvasState = {
 objects: [],
 background: fabricCanvas.backgroundColor || '#ffffff'
 };

 // Get all objects from the canvas and ensure they have IDs
 fabricCanvas.getObjects().forEach(obj => {
 if (obj.toObject) {
 // Ensure object has an ID
 if (!obj.id) {
 obj.id = generateObjectId();
 console.log('🆔 Generated ID for existing object:', obj.type, obj.id);
 }

 const objData = obj.toObject();
 objData.id = obj.id; // Ensure ID is included in serialized data
 currentCanvasState.objects.push(objData);
 }
 });

 console.log('🎨 Sending canvas state with room creation:', {
 objectCount: currentCanvasState.objects.length,
 background: currentCanvasState.background
 });

 collaborationSocket.send(JSON.stringify({
 type: 'create_room',
 room_name: roomName,
 max_users: maxUsers,
 initial_canvas_state: currentCanvasState
 }));
 }

 createRoomModal.classList.add('hidden');
 document.getElementById('room-name-input').value = '';
 });

 document.getElementById('confirm-join-room').addEventListener('click', () => {
 const roomId = document.getElementById('room-id-input').value.trim().toUpperCase();
 const userName = document.getElementById('user-name-input').value.trim();

 if (!roomId) {
 alert('Please enter a room ID');
 return;
 }

 if (!userName) {
 alert('Please enter your display name');
 return;
 }

 // Update stored user name
 localStorage.setItem('userName', userName);

 // Update user name on server if already connected
 if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
 collaborationSocket.send(JSON.stringify({
 type: 'update_name',
 name: userName
 }));

 // Small delay to ensure name update is processed before joining room
 setTimeout(() => {
 collaborationSocket.send(JSON.stringify({
 type: 'join_room',
 room_id: roomId
 }));
 }, 100);
 } else {
 // If not connected, connect first then join
 connectToCollaborationServer();
 setTimeout(() => {
 if (collaborationSocket && collaborationSocket.readyState === WebSocket.OPEN) {
 collaborationSocket.send(JSON.stringify({
 type: 'join_room',
 room_id: roomId
 }));
 }
 }, 500);
 }

 joinRoomModal.classList.add('hidden');
 document.getElementById('room-id-input').value = '';
 document.getElementById('user-name-input').value = '';
 });

 // Close modals when clicking outside
 createRoomModal.addEventListener('click', (e) => {
 if (e.target === createRoomModal) {
 createRoomModal.classList.add('hidden');
 }
 });

 joinRoomModal.addEventListener('click', (e) => {
 if (e.target === joinRoomModal) {
 joinRoomModal.classList.add('hidden');
 }
 });

 // --- Video/Audio Event Handlers ---
 toggleVideoBtn.addEventListener('click', async () => {
 console.log('Video button clicked, isCallActive:', isCallActive);

 if (!isCallActive) {
 console.log('Starting video call...');
 await startVideoCall();
 } else {
 console.log('Toggling video...');
 toggleVideo();
 }
 });

 toggleAudioBtn.addEventListener('click', async () => {
 console.log('Audio button clicked, isCallActive:', isCallActive);

 if (!isCallActive) {
 console.log('Starting video call...');
 await startVideoCall();
 } else {
 console.log('Toggling audio...');
 toggleAudio();
 }
 });

 endMeetingBtn.addEventListener('click', endVideoCall);

 // Toolbar media button handlers
 const toolbarVideoBtn = document.getElementById('toggle-video');
 const toolbarAudioBtn = document.getElementById('toggle-audio');

 if (toolbarVideoBtn) {
 toolbarVideoBtn.addEventListener('click', async () => {
 if (!isCallActive) {
 await startVideoCall();
 } else {
 toggleVideo();
 }
 });
 }

 if (toolbarAudioBtn) {
 toolbarAudioBtn.addEventListener('click', async () => {
 if (!isCallActive) {
 await startVideoCall();
 } else {
 toggleAudio();
 }
 });
 }

 // Debug: Test if event handlers are attached
 console.log('Event handlers attached successfully');

 // Add simple test handlers to verify buttons work
 if (toggleVideoBtn) {
 toggleVideoBtn.addEventListener('mouseenter', () => {
 console.log('Video button hovered - button is responsive');
 });
 }

 if (toggleAudioBtn) {
 toggleAudioBtn.addEventListener('mouseenter', () => {
 console.log('Audio button hovered - button is responsive');
 });
 }

 // --- Enhanced Canvas Collaboration Events ---

 // Track object selection changes
 fabricCanvas.on('selection:created', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId) {
 const selectedObjects = e.selected || [];

 // Ensure all selected objects have IDs
 selectedObjects.forEach(obj => {
 if (!obj.id) {
 obj.id = generateObjectId();
 console.log('🆔 Generated missing ID for selected object:', obj.type, obj.id);
 }
 });

 console.log('👆 Selection created:', selectedObjects.map(obj => ({type: obj.type, id: obj.id})));
 sendCanvasOperation('selection_created', {
 object_ids: selectedObjects.map(obj => obj.id).filter(id => id),
 user_id: currentUserId
 });
 }
 });

 fabricCanvas.on('selection:updated', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId) {
 const selectedObjects = e.selected || [];

 // Ensure all selected objects have IDs
 selectedObjects.forEach(obj => {
 if (!obj.id) {
 obj.id = generateObjectId();
 console.log('🆔 Generated missing ID for updated selection:', obj.type, obj.id);
 }
 });

 console.log('👆 Selection updated:', selectedObjects.map(obj => ({type: obj.type, id: obj.id})));
 sendCanvasOperation('selection_updated', {
 object_ids: selectedObjects.map(obj => obj.id).filter(id => id),
 user_id: currentUserId
 });
 }
 });

 fabricCanvas.on('selection:cleared', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId) {
 console.log('👆 Selection cleared');
 sendCanvasOperation('selection_cleared', {
 user_id: currentUserId
 });
 }
 });

 // Track path creation (drawing) - this handles ALL drawing strokes
 fabricCanvas.on('path:created', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId) {
 const path = e.path;
 if (!path.id) {
 path.id = generateObjectId();
 }

 // Track this path ID to prevent duplicate object:added events
 recentPathIds.add(path.id);
 setTimeout(() => {
 recentPathIds.delete(path.id);
 }, 1000); // Clean up after 1 second

 console.log('🎨 Path created locally:', path.id);
 sendCanvasOperation('path_created', {
 path: path.toObject(),
 object_id: path.id
 });
 }
 });

 // Track object addition - this handles shapes, text, images, etc. (NOT drawing paths)
 fabricCanvas.on('object:added', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId && e.target) {
 const obj = e.target;

 console.log('🎨 Object added event - type:', obj.type, 'class:', obj.constructor.name, 'id:', obj.id);

 // Skip if this object was recently created as a path
 if (obj.id && recentPathIds.has(obj.id)) {
 console.log('⏭️ Skipping object:added for recent path:', obj.id);
 return;
 }

 // COMPLETELY SKIP all paths - they are ONLY handled by path:created
 if (obj.type === 'path' || obj.constructor.name === 'Path' || obj.constructor.name === 'fabric.Path') {
 console.log('⏭️ Skipping path in object:added (handled by path:created)', obj.type, obj.constructor.name);
 return;
 }

 // Only handle non-path objects (rectangles, circles, text, images, etc.)
 if (!obj.id) {
 obj.id = generateObjectId();
 }
 console.log('🎨 Non-path object added locally:', obj.type, obj.id);
 sendCanvasOperation('object_added', {
 object: obj.toObject(),
 object_id: obj.id
 });
 }
 });

 // Track object modifications (move, resize, rotate)
 fabricCanvas.on('object:modified', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId && e.target) {
 const obj = e.target;
 if (!obj.id) {
 obj.id = generateObjectId();
 }
 sendCanvasOperation('object_modified', {
 object_id: obj.id,
 object: obj.toObject(),
 transform: {
 left: obj.left,
 top: obj.top,
 scaleX: obj.scaleX,
 scaleY: obj.scaleY,
 angle: obj.angle
 }
 });
 }
 });

 // Track object movement during drag
 fabricCanvas.on('object:moving', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId && e.target) {
 const obj = e.target;
 if (!obj.id) {
 obj.id = generateObjectId();
 }
 // Throttle movement updates to avoid flooding
 const now = Date.now();
 if (now - lastOperationTime > 50) { // 20fps max
 sendCanvasOperation('object_moving', {
 object_id: obj.id,
 position: {
 left: obj.left,
 top: obj.top
 }
 }, 'high'); // High priority for real-time movement
 lastOperationTime = now;
 }
 }
 });

 // Track object scaling
 fabricCanvas.on('object:scaling', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId && e.target) {
 const obj = e.target;
 if (!obj.id) {
 obj.id = generateObjectId();
 }
 const now = Date.now();
 if (now - lastOperationTime > 50) {
 sendCanvasOperation('object_scaling', {
 object_id: obj.id,
 scale: {
 scaleX: obj.scaleX,
 scaleY: obj.scaleY
 }
 }, 'high');
 lastOperationTime = now;
 }
 }
 });

 // Track object rotation
 fabricCanvas.on('object:rotating', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId && e.target) {
 const obj = e.target;
 if (!obj.id) {
 obj.id = generateObjectId();
 }
 const now = Date.now();
 if (now - lastOperationTime > 50) {
 sendCanvasOperation('object_rotating', {
 object_id: obj.id,
 angle: obj.angle
 }, 'high');
 lastOperationTime = now;
 }
 }
 });

 // Track object removal
 fabricCanvas.on('object:removed', (e) => {
 if (!isProcessingRemoteOperation && currentRoomId && e.target) {
 const obj = e.target;
 if (!obj.id) {
 console.warn('Object removed without ID, cannot sync');
 return;
 }
 sendCanvasOperation('object_removed', {
 object_id: obj.id,
 object_type: obj.type
 });
 }
 });

 // Track mouse movement for cursor sharing
 let lastCursorSend = 0;
 fabricCanvas.on('mouse:move', (e) => {
 if (currentRoomId && Date.now() - lastCursorSend > 50) { // Throttle to 20fps
 const pointer = fabricCanvas.getPointer(e.e);
 sendCursorPosition(pointer.x, pointer.y);
 lastCursorSend = Date.now();
 }
 });



 // --- Resizable AI Panel Functionality ---
 let isResizing = false;
 let isChatSidebarVisible = true;

 // Load saved sidebar width
 const savedSidebarWidth = localStorage.getItem('aiSidebarWidth');
 if (savedSidebarWidth) {
 aiChatSidebar.style.width = savedSidebarWidth + 'px';
 }

 // Resize handle functionality
 resizeHandle.addEventListener('mousedown', (e) => {
 isResizing = true;
 document.body.style.cursor = 'col-resize';
 document.body.style.userSelect = 'none';

 const startX = e.clientX;
 const startWidth = parseInt(document.defaultView.getComputedStyle(aiChatSidebar).width, 10);

 function doResize(e) {
 if (!isResizing) return;

 const newWidth = startWidth - (e.clientX - startX);
 const minWidth = 300;
 const maxWidth = 600;

 if (newWidth >= minWidth && newWidth <= maxWidth) {
 aiChatSidebar.style.width = newWidth + 'px';
 localStorage.setItem('aiSidebarWidth', newWidth);
 }
 }

 function stopResize() {
 isResizing = false;
 document.body.style.cursor = '';
 document.body.style.userSelect = '';
 document.removeEventListener('mousemove', doResize);
 document.removeEventListener('mouseup', stopResize);
 }

 document.addEventListener('mousemove', doResize);
 document.addEventListener('mouseup', stopResize);
 });

 // Toggle sidebar visibility
 toggleChatSidebarBtn.addEventListener('click', () => {
 isChatSidebarVisible = false;
 aiChatSidebar.style.display = 'none';
 openAiChatBtn.classList.remove('hidden');
 openAiChatToolbarBtn.classList.remove('hidden');
 localStorage.setItem('aiSidebarVisible', false);
 });

 // Open AI chat button functionality (meeting bar)
 openAiChatBtn.addEventListener('click', () => {
 isChatSidebarVisible = true;
 aiChatSidebar.style.display = 'flex';
 openAiChatBtn.classList.add('hidden');
 openAiChatToolbarBtn.classList.add('hidden');
 localStorage.setItem('aiSidebarVisible', true);
 });

 // Open AI chat button functionality (toolbar)
 openAiChatToolbarBtn.addEventListener('click', () => {
 isChatSidebarVisible = true;
 aiChatSidebar.style.display = 'flex';
 openAiChatBtn.classList.add('hidden');
 openAiChatToolbarBtn.classList.add('hidden');
 localStorage.setItem('aiSidebarVisible', true);
 });

 // Load saved sidebar visibility
 const savedSidebarVisibility = localStorage.getItem('aiSidebarVisible');
 if (savedSidebarVisibility === 'false') {
 isChatSidebarVisible = false;
 aiChatSidebar.style.display = 'none';
 openAiChatBtn.classList.remove('hidden');
 openAiChatToolbarBtn.classList.remove('hidden');
 }

 // Connect to collaboration server on load
 connectToCollaborationServer();

 // Initial typesetting for welcome message
 if (typeof MathJax !== 'undefined' && MathJax.typesetPromise) {
 MathJax.typesetPromise([chatHistory]).catch((err) => console.error('MathJax initial typesetting error:', err));
 }

 console.log("Modern UI Notebook script fully initialized.");

 // --- Debug Functions (for testing) ---
 window.debugVideoCallStatus = function() {
 console.log('=== Video Call Debug Status ===');
 console.log('isCallActive:', isCallActive);
 console.log('localStream:', localStream);
 console.log('peerConnections:', peerConnections);
 console.log('roomUsers:', roomUsers);
 console.log('currentUserId:', currentUserId);
 console.log('AI Chat Sidebar visible:', isChatSidebarVisible);
 console.log('AI Chat reopen buttons:');
 console.log('- Meeting bar button hidden:', openAiChatBtn?.classList.contains('hidden'));
 console.log('- Toolbar button hidden:', openAiChatToolbarBtn?.classList.contains('hidden'));
 console.log('================================');
 };

 window.testAIChatReopen = function() {
 console.log('Testing AI Chat reopen functionality...');
 if (isChatSidebarVisible) {
 console.log('Closing AI chat sidebar...');
 toggleChatSidebarBtn.click();
 setTimeout(() => {
 console.log('Checking if reopen buttons are visible...');
 const meetingBarBtnVisible = !openAiChatBtn.classList.contains('hidden');
 const toolbarBtnVisible = !openAiChatToolbarBtn.classList.contains('hidden');
 console.log('Meeting bar button visible:', meetingBarBtnVisible);
 console.log('Toolbar button visible:', toolbarBtnVisible);
 if (meetingBarBtnVisible || toolbarBtnVisible) {
 console.log('✅ AI Chat reopen buttons are working correctly!');
 } else {
 console.log('❌ AI Chat reopen buttons are not visible!');
 }
 }, 100);
 } else {
 console.log('AI chat is already closed. Checking button visibility...');
 const meetingBarBtnVisible = !openAiChatBtn.classList.contains('hidden');
 const toolbarBtnVisible = !openAiChatToolbarBtn.classList.contains('hidden');
 console.log('Meeting bar button visible:', meetingBarBtnVisible);
 console.log('Toolbar button visible:', toolbarBtnVisible);
 }
 };
});
</script>

<!-- Room Creation Modal -->
<div id="create-room-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
 <div class="bg-white dark:bg-slate-800 rounded-lg p-6 w-96 max-w-md mx-4">
 <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">Create Collaboration Room</h3>
 <div class="space-y-4">
 <div>
 <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Room Name (Optional)</label>
 <input type="text" id="room-name-input" placeholder="My Canvas Room" class="w-full p-2.5 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200">
 </div>
 <div>
 <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Max Users</label>
 <select id="max-users-select" class="w-full p-2.5 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200">
 <option value="5">5 users</option>
 <option value="10" selected>10 users</option>
 <option value="20">20 users</option>
 <option value="50">50 users</option>
 </select>
 </div>
 </div>
 <div class="flex justify-end space-x-3 mt-6">
 <button id="cancel-create-room" class="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">Cancel</button>
 <button id="confirm-create-room" class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors">Create Room</button>
 </div>
 </div>
</div>

<!-- Join Room Modal -->
<div id="join-room-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
 <div class="bg-white dark:bg-slate-800 rounded-lg p-6 w-96 max-w-md mx-4">
 <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">Join Collaboration Room</h3>
 <div class="space-y-4">
 <div>
 <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Room ID</label>
 <input type="text" id="room-id-input" placeholder="Enter room ID..." class="w-full p-2.5 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200">
 </div>
 <div>
 <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Your Name</label>
 <input type="text" id="user-name-input" placeholder="Your display name..." class="w-full p-2.5 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200">
 </div>
 </div>
 <div class="flex justify-end space-x-3 mt-6">
 <button id="cancel-join-room" class="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">Cancel</button>
 <button id="confirm-join-room" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">Join Room</button>
 </div>
 </div>
</div>

<!-- User Cursors Container -->
<div id="user-cursors" class="absolute inset-0 pointer-events-none z-40"></div>

</body>
</html>
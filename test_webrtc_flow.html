<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .button.primary { background-color: #007bff; color: white; }
        .button.success { background-color: #28a745; color: white; }
        .button.warning { background-color: #ffc107; color: black; }
        .button.danger { background-color: #dc3545; color: white; }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.pending { background-color: #ffc107; color: black; }
        .status.success { background-color: #28a745; color: white; }
        .status.error { background-color: #dc3545; color: white; }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebRTC Bidirectional Communication Test</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions</h3>
            <p><strong>CRITICAL FIXES IMPLEMENTED:</strong></p>
            <ul>
                <li>✅ Fixed WebRTC offer handling for users not in call</li>
                <li>✅ Added user ID-based connection initiation to prevent conflicts</li>
                <li>✅ Removed premature peer connection creation</li>
                <li>✅ Enhanced debugging and error handling</li>
            </ul>
            <p><strong>To test bidirectional video/audio:</strong></p>
            <ol>
                <li>Open the main app in <strong>TWO</strong> browser windows</li>
                <li>Both users join the <strong>SAME</strong> room (e.g., "test-room")</li>
                <li>User 1: Click "Start Video Meeting"</li>
                <li>User 2: Click "Join Call" button</li>
                <li>Verify both users can see and hear each other</li>
                <li>Test mute/unmute controls</li>
            </ol>
        </div>

        <div class="test-step">
            <h3>Step 1: Open Main Application</h3>
            <button class="button primary" onclick="openMainApp()">Open Main App</button>
            <span id="step1Status" class="status pending">Pending</span>
            <p>This will open the main Flux Notebook application in a new tab.</p>
        </div>

        <div class="test-step">
            <h3>Step 2: Test WebRTC Debug Functions</h3>
            <button class="button warning" onclick="showDebugInstructions()">Show Debug Commands</button>
            <span id="step2Status" class="status pending">Pending</span>
            <div id="debugInstructions" style="display: none;" class="log">
In the browser console (F12), you can use these debug commands:

1. debugWebRTCConnections() - Shows detailed WebRTC state
2. testWebRTCFlow() - Tests the WebRTC initialization flow
3. debugVideoCallStatus() - Shows video call status

Example usage:
> debugWebRTCConnections()
> testWebRTCFlow()
            </div>
        </div>

        <div class="test-step">
            <h3>Step 3: Expected Behavior</h3>
            <button class="button success" onclick="showExpectedBehavior()">Show Expected Results</button>
            <span id="step3Status" class="status pending">Pending</span>
            <div id="expectedBehavior" style="display: none;" class="log">
✅ EXPECTED BEHAVIOR:

1. User 1 starts call:
   - Sees own video immediately
   - No peer connections created yet
   - Other users see notification + Join button

2. User 2 joins call:
   - Gets media access
   - Creates peer connection to User 1 (if User2 ID > User1 ID)
   - User 1 receives user_joined_call message
   - User 1 creates peer connection to User 2 (if User1 ID < User2 ID)

3. WebRTC Exchange:
   - Offers/answers exchanged through server
   - ICE candidates exchanged
   - Media streams connected bidirectionally

4. Result:
   - Both users see each other's video
   - Both users hear each other's audio
   - Mute/unmute works independently
            </div>
        </div>

        <div class="test-step">
            <h3>Step 4: Troubleshooting</h3>
            <button class="button danger" onclick="showTroubleshooting()">Show Troubleshooting</button>
            <span id="step4Status" class="status pending">Pending</span>
            <div id="troubleshooting" style="display: none;" class="log">
🔧 TROUBLESHOOTING:

If video/audio is not working:

1. Check browser console for errors
2. Verify both users are in the same room
3. Ensure camera/microphone permissions are granted
4. Check WebRTC connection states using debugWebRTCConnections()
5. Verify STUN servers are accessible
6. Test with different browsers (Chrome, Firefox, Safari)

Common issues:
- Firewall blocking WebRTC traffic
- No HTTPS (required for getUserMedia in production)
- Browser compatibility issues
- Network NAT/firewall restrictions
            </div>
        </div>

        <div class="test-step">
            <h3>Test Log</h3>
            <button class="button primary" onclick="clearTestLog()">Clear Log</button>
            <div id="testLog" class="log">Test log will appear here...\n</div>
        </div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearTestLog() {
            document.getElementById('testLog').textContent = 'Test log cleared...\n';
        }

        function openMainApp() {
            window.open('frontend.html', '_blank');
            document.getElementById('step1Status').className = 'status success';
            document.getElementById('step1Status').textContent = 'Opened';
            log('Main application opened in new tab');
        }

        function showDebugInstructions() {
            const element = document.getElementById('debugInstructions');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
            document.getElementById('step2Status').className = 'status success';
            document.getElementById('step2Status').textContent = 'Shown';
            log('Debug instructions displayed');
        }

        function showExpectedBehavior() {
            const element = document.getElementById('expectedBehavior');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
            document.getElementById('step3Status').className = 'status success';
            document.getElementById('step3Status').textContent = 'Shown';
            log('Expected behavior displayed');
        }

        function showTroubleshooting() {
            const element = document.getElementById('troubleshooting');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
            document.getElementById('step4Status').className = 'status success';
            document.getElementById('step4Status').textContent = 'Shown';
            log('Troubleshooting guide displayed');
        }

        // Auto-start
        window.onload = function() {
            log('WebRTC Flow Test Ready');
            log('Click "Open Main App" to start testing');
        };
    </script>
</body>
</html>
